{"header": {"menu": {"advanced": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "connect-wallet": "Wallet verbinden", "menu": "<PERSON><PERSON>", "wallet": {"disconnect": "<PERSON><PERSON><PERSON>", "not-installed": "<PERSON>et nicht installiert", "not-installed-description": "Bitte installieren Si<PERSON> eine Wallet, um sich zu verbinden.", "address-copied": "<PERSON><PERSON><PERSON> kop<PERSON>t!", "copy-address": "<PERSON><PERSON><PERSON> k<PERSON>"}}, "languages": {"en": "<PERSON><PERSON><PERSON>", "de": "De<PERSON>ch"}}, "home": {"sections": {"featured-coins": {"title": "Empfohlene Coins", "description": "Die heißesten Token, die jeder gerade beobachtet", "card": {"market-cap": "Marktkapitalisierung"}, "filters": {"search-placeholder": "Empfohlene Coins durchsuchen", "last-trade": "<PERSON><PERSON><PERSON> Handel", "creation-time": "Erstellungszeit", "heading-up": "Aufwärtstrend", "watchlist": "Beobachtungsliste", "all-tokens": "Alle Token"}}}}, "token-detail": {"header-section": {"created-by": "<PERSON><PERSON><PERSON><PERSON> von", "contract-address": "Vertragsadresse", "market-cap": "Marktkapitalisierung", "time-created": "Erstellungszeit"}, "activity-table": {"title": "Aktivität", "date": "Datum", "type": "<PERSON><PERSON>", "price": "Pre<PERSON>", "mini": "Mini", "account": "Ko<PERSON>"}, "buy-sell-form": {"buy": "<PERSON><PERSON><PERSON>", "sell": "Verkaufen", "half": "H<PERSON><PERSON><PERSON>", "max": "Maximum", "slippage": "Slippage", "amount": "Betrag"}, "coin-info": {"about-coin": "Über den Coin", "supply": "<PERSON><PERSON><PERSON>", "created": "<PERSON><PERSON><PERSON><PERSON>", "trade-fees": "Handelsgebühren", "contract-address": "Vertragsadresse", "developer-address": "Entwickleradresse"}, "comment-list": {"add-reply": "Antwort hinzufügen"}, "market-cap-progress": {"market-cap-progress": "Marktkapitalisierungs-Fortschritt", "current": "Aktuell", "target": "<PERSON><PERSON>"}, "top-holders-table": {"title": "Top-In<PERSON>er", "holder": "<PERSON><PERSON><PERSON>", "percentage": "Prozentsatz (%)"}, "tabs-section": {"replies": "Antworten", "coin-info": "Coin-Info", "activity": "Aktivität", "top-holders": "Top-In<PERSON>er"}}, "advanced": {"advanced-section": {"newly-created": "<PERSON><PERSON>", "graduating": "Abschließend", "graduated": "Abgeschlossen", "watchlist": "Beobachtungsliste"}, "advanced-card": {"progress": "Fort<PERSON><PERSON>t"}}, "footer": {"menu": {"request-api": "API anfordern", "verify-token": "Token verifizieren", "docs": "Dokumentation"}}, "theme": {"dark": "<PERSON><PERSON><PERSON>", "light": "Hell"}, "components": {"launch-token": {"label": "Token starten"}, "address": {"copy-address": "<PERSON><PERSON><PERSON> k<PERSON>", "copied": "Kopiert!", "view-on-explorer": "Im Explorer anzeigen"}, "image-upload": {"upload-error": "<PERSON><PERSON> beim Hochladen des Bildes: {message}", "unknown-error": "Ein unbekannter Fehler ist aufgetreten", "uploading-picture": {"title": "Bild wird hoch<PERSON>aden", "description": "Aktualisieren Sie nicht oder führen Sie andere Aktionen durch, während es hochgeladen wird"}, "form": {"title": "Bild hochladen", "description": "Maximale Dateigröße: 5MB. Unterstützte Formate: JPEG, PNG, GIF, WebP"}, "image-uploaded": {"title": "Bild hoch<PERSON>aden", "description": "<PERSON><PERSON><PERSON>, um ein anderes Bild hochzuladen"}}}, "dialogs": {"launch-token": {"title": "Token erstellen", "description": "<PERSON><PERSON><PERSON><PERSON> und starten Sie Ihren eigenen Token auf der Blockchain", "form": {"optional": "Optional", "hide-options": "Optionen ausblenden", "show-more-options": "Weitere Optionen anzeigen", "next": "<PERSON><PERSON>", "cancel": "Abbrechen", "image": {"validation": {"required": "Bild ist erforderlich", "file-size": "Dateigr<PERSON><PERSON> muss unter 5MB liegen", "file-type": "Nur JPEG-, PNG-, GIF- und WebP-Dateien sind erlaubt", "url": "Bild-URL muss eine gültige URL sein"}}, "name": {"label": "Token-Name", "placeholder": "Token-Name eingeben", "validation": {"required": "Token-Name ist erforderlich", "max": "Token-Name darf maximal 32 <PERSON><PERSON><PERSON> lang sein"}}, "symbol": {"label": "Token-Symbol", "validation": {"required": "Token-Symbol ist erforderlich", "max": "Token-Symbol darf maximal 10 Zeichen lang sein"}}, "decimals": {"label": "Dezimalstellen", "validation": {"min": "Dezimalstellen müssen eine nicht-negative ganze Zahl sein", "max": "Dezimalstellen müssen 18 oder weniger betragen"}}, "custom-token-supply": {"validation": {"min": "Benutzerdefiniertes Angebot muss mindestens 10 Millionen betragen", "positive": "Benutzerdefiniertes Angebot muss positiv sein", "required": "Benutzerdefiniertes Token-Angebot ist erforderlich und muss mindestens 10 Millionen betragen"}}, "platform": {"label": "Plattform"}, "description": {"label": "Beschreibung", "placeholders": {"1": "Die Zukunft von DeFi ist hier 🚀", "2": "Revolutionärer Token für die nächste Generation", "3": "Innovation in den Blockchain-Bereich bringen", "4": "Community-getriebener Token mit echtem Nutzen", "5": "Der Memecoin, der zum Mond fliegt 🌙", "6": "Von der Community betrieben, für die Community", "7": "Token der nächsten Generation mit unbegrenztem Potenzial", "8": "Schließe dich der Revolution an und halte stark 💎", "9": "Die Zukunft bauen, Block für Block", "10": "Träume in die Realität tokenisieren", "11": "Der ultimative Wertspeicher in Web3", "12": "Dezentralisiert, deflationär und degen-approved"}}, "website": {"label": "Website", "validation": {"url": "<PERSON>te geben Si<PERSON> eine gültige URL ein"}}, "twitter": {"label": "Twitter", "validation": {"url": "<PERSON>te geben Si<PERSON> eine gültige URL ein"}}, "telegram": {"label": "Telegram", "validation": {"url": "<PERSON>te geben Si<PERSON> eine gültige URL ein"}}, "supply-token": {"label": "Gesamtangebot Token", "custom": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "label": "Benutzerdefiniertes Angebot Token"}}}}}}