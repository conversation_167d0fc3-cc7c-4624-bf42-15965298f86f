# Frontend

This frontend will look similar to letsbonk.fun. It will:

- display latest coins (poll a route of '/list' every 5s)
- connect via web socket to server to listen to trading view chart changes
- users can create new coins

Pages

- home page: update / filter latest coins
- create coin modal
- token page
  - show replies
  - show coin info
  - show activity
  - show top holders
- revenue page (use placeholder data for now)
  - show total fees generated
  - show total fees today
