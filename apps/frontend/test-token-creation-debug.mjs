// Debug script to test token creation transactions
// This will help us identify the specific error in place_dev_order and accept_connector

import { SuiClient } from '@mysten/sui/client';
import { Transaction } from '@mysten/sui/transactions';

const client = new SuiClient({
  url: 'https://fullnode.devnet.sui.io',
});

// Configuration from deployments.json
const config = {
  hopfunPackageId:
    '0x497ff2769b28664d052601132542a2a77e75339945dc34a494e40fcd680065ac',
  memeConfigId:
    '0x864be1505bd5f86cae41741293d02860039007ebf2eeb1374c3dfebc246251d2',
  hopdexConfigId:
    '0x11231b125c38dba00f5f092a721a976eb19c5fa8084ac34b4e5da52e5b160631',
};

// Existing connector from our analysis
const testConnector = {
  objectId:
    '0xd40fe3c11ca8ab93a8d79e05010f739ef399ca552ce4e7fe693d70c7274fbb16',
  tempId: 123,
  creator: '0xfd78cd8b8351aff637c34a0272f26a3e694028890d1b637563b4aa2e90dd496a',
  coinType:
    '0x49cd458aa9d2fff2c305be1c77452c48ab24f4e654b76299fd9b9574301a5929::template::TEMPLATE',
};

async function debugTokenCreation() {
  console.log('🧪 Debugging token creation transactions...');

  try {
    // Test 1: Check if the connector still exists
    console.log('1️⃣ Checking if connector still exists...');
    const connectorObject = await client.getObject({
      id: testConnector.objectId,
      options: { showContent: true, showOwner: true, showType: true },
    });

    if (!connectorObject.data) {
      console.log('❌ Connector no longer exists - it may have been processed');
      return;
    }

    console.log('✅ Connector exists:', {
      id: connectorObject.data.objectId,
      owner: connectorObject.data.owner,
      type: connectorObject.data.type,
    });

    // Test 2: Check MemeConfig for DevOrder entries
    console.log('2️⃣ Checking MemeConfig for DevOrder entries...');
    const memeConfig = await client.getObject({
      id: config.memeConfigId,
      options: { showContent: true },
    });

    console.log('✅ MemeConfig exists:', {
      id: memeConfig.data?.objectId,
      fields: memeConfig.data?.content?.fields,
    });

    // Test 3: Try to create a place_dev_order transaction (dry run)
    console.log('3️⃣ Testing place_dev_order transaction structure...');
    const placeOrderTx = new Transaction();

    // Split 0 SUI for the transaction (minimum required)
    const [zeroCoin] = placeOrderTx.splitCoins(placeOrderTx.gas, [0]);

    placeOrderTx.moveCall({
      target: `${config.hopfunPackageId}::meme::place_dev_order`,
      arguments: [
        placeOrderTx.object(config.memeConfigId),
        placeOrderTx.pure.u64(testConnector.tempId),
        zeroCoin,
      ],
    });

    // Set sender for the transaction
    placeOrderTx.setSender(testConnector.creator);

    // Dry run to see if the transaction is valid
    console.log('🔍 Dry running place_dev_order transaction...');
    const dryRunResult = await client.dryRunTransactionBlock({
      transactionBlock: await placeOrderTx.build({ client }),
    });

    console.log('📊 Dry run result:', {
      status: dryRunResult.effects.status,
      error:
        dryRunResult.effects.status.status === 'failure'
          ? dryRunResult.effects.status.error
          : null,
    });

    if (dryRunResult.effects.status.status === 'failure') {
      console.error(
        '❌ place_dev_order transaction would fail:',
        dryRunResult.effects.status.error,
      );
    } else {
      console.log('✅ place_dev_order transaction structure is valid');
    }

    // Test 4: Try to create an accept_connector transaction (dry run)
    console.log('4️⃣ Testing accept_connector transaction structure...');

    // First, we need to find the CoinMetadata object for this coin type
    console.log('🔍 Looking for CoinMetadata object...');

    // Extract package ID from coin type
    const coinPackageId = testConnector.coinType.split('::')[0];
    console.log('📦 Coin package ID:', coinPackageId);

    // Query for CoinMetadata objects from this package
    const coinMetadataObjects = await client.getOwnedObjects({
      owner: coinPackageId,
      filter: {
        StructType: `0x2::coin::CoinMetadata<${testConnector.coinType}>`,
      },
      options: { showContent: true, showType: true },
    });

    console.log(
      '📋 Found CoinMetadata objects:',
      coinMetadataObjects.data.length,
    );

    if (coinMetadataObjects.data.length === 0) {
      console.log('⚠️ No CoinMetadata found - checking for frozen objects...');

      // Try to find frozen CoinMetadata by querying events
      const publishEvents = await client.queryEvents({
        query: {
          MoveEventType: '0x2::package::PublishEvent',
        },
        limit: 50,
        order: 'descending',
      });

      console.log('📊 Found publish events:', publishEvents.data.length);

      // Look for the specific package publish event
      const packagePublishEvent = publishEvents.data.find(
        (event) => event.parsedJson?.package_id === coinPackageId,
      );

      if (packagePublishEvent) {
        console.log(
          '✅ Found package publish event:',
          packagePublishEvent.id.txDigest,
        );

        // Get the transaction details to find created objects
        const publishTx = await client.getTransactionBlock({
          digest: packagePublishEvent.id.txDigest,
          options: { showObjectChanges: true },
        });

        // Look for CoinMetadata in created objects
        const coinMetadata = publishTx.objectChanges?.find(
          (change) =>
            change.type === 'created' &&
            change.objectType?.includes('::coin::CoinMetadata<') &&
            change.objectType?.includes(testConnector.coinType),
        );

        if (coinMetadata) {
          console.log('✅ Found CoinMetadata object:', coinMetadata.objectId);

          // Now test accept_connector transaction
          const acceptTx = new Transaction();

          acceptTx.moveCall({
            target: `${config.hopfunPackageId}::meme::accept_connector`,
            arguments: [
              acceptTx.object(config.hopdexConfigId),
              acceptTx.object(config.memeConfigId),
              acceptTx.object(testConnector.objectId),
              acceptTx.object(coinMetadata.objectId),
            ],
            typeArguments: [testConnector.coinType],
          });

          acceptTx.setSender(testConnector.creator);

          console.log('🔍 Dry running accept_connector transaction...');
          const acceptDryRun = await client.dryRunTransactionBlock({
            transactionBlock: await acceptTx.build({ client }),
          });

          console.log('📊 Accept connector dry run result:', {
            status: acceptDryRun.effects.status,
            error:
              acceptDryRun.effects.status.status === 'failure'
                ? acceptDryRun.effects.status.error
                : null,
          });

          if (acceptDryRun.effects.status.status === 'failure') {
            console.error(
              '❌ accept_connector transaction would fail:',
              acceptDryRun.effects.status.error,
            );
          } else {
            console.log('✅ accept_connector transaction structure is valid');
          }
        } else {
          console.log(
            '❌ Could not find CoinMetadata object in publish transaction',
          );
        }
      } else {
        console.log('❌ Could not find package publish event');
      }
    }
  } catch (error) {
    console.error('❌ Debug failed:', error);
  }
}

debugTokenCreation();
