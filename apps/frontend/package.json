{"name": "frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "dev:turbo": "next --turbopack", "build": "next build", "start": "next start", "lint": "next lint --fix", "format": "prettier --write .", "format:check": "prettier --check .", "test": "vitest run", "test:watch": "vitest watch", "type-check": "tsc --noEmit"}, "dependencies": {"@hookform/resolvers": "catalog:", "@mysten/dapp-kit": "catalog:", "@mysten/sui": "catalog:", "@radix-ui/react-accordion": "catalog:", "@radix-ui/react-dialog": "catalog:", "@radix-ui/react-dropdown-menu": "catalog:", "@radix-ui/react-label": "catalog:", "@radix-ui/react-progress": "catalog:", "@radix-ui/react-scroll-area": "catalog:", "@radix-ui/react-select": "catalog:", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "catalog:", "@radix-ui/react-tabs": "catalog:", "@radix-ui/react-toggle": "catalog:", "@radix-ui/react-toggle-group": "catalog:", "@radix-ui/react-tooltip": "catalog:", "@tanstack/react-query": "catalog:", "axios": "catalog:", "clsx": "catalog:", "date-fns": "catalog:", "flag-icons": "catalog:", "lucide-react": "catalog:", "next": "catalog:", "next-intl": "catalog:", "next-themes": "catalog:", "react": "catalog:", "react-dom": "catalog:", "react-dropzone": "catalog:", "react-hook-form": "catalog:", "react-markdown": "catalog:", "rehype-highlight": "catalog:", "remark-gfm": "catalog:", "sonner": "catalog:", "tailwind-merge": "catalog:", "tailwind-variants": "catalog:", "zod": "catalog:"}, "devDependencies": {"@hopfun/eslint": "workspace:*", "@hopfun/tsconfig": "workspace:*", "@next/eslint-plugin-next": "catalog:", "@svgr/webpack": "catalog:", "@tailwindcss/postcss": "catalog:", "@testing-library/dom": "catalog:", "@testing-library/jest-dom": "catalog:", "@testing-library/react": "catalog:", "@types/node": "catalog:", "@types/react": "catalog:", "@types/react-dom": "catalog:", "@vitejs/plugin-react": "catalog:", "eslint": "catalog:", "jsdom": "catalog:", "postcss": "catalog:", "prettier": "catalog:", "prettier-plugin-tailwindcss": "catalog:", "sass": "catalog:", "sass-loader": "catalog:", "tailwindcss": "catalog:", "tw-animate-css": "catalog:", "typescript": "catalog:", "vite": "catalog:", "vitest": "catalog:"}}