import { MoveRightIcon } from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';
import type { ComponentPropsWithoutRef } from 'react';

import { Button } from '@/components/ui/button';
import { cn } from '@/utils/classnames';

interface HeroCardProps extends ComponentPropsWithoutRef<'div'> {
  id: string;
  address: string;
  title: string;
  currency: string;
  imageUrl: string;
  emoji?: string;
}

export const HeroCard = ({
  id,
  address,
  title,
  currency,
  imageUrl,
  className,
  emoji,
}: HeroCardProps) => {
  return (
    <Link
      prefetch
      className={cn(
        'border-primary/40 bg-background flex flex-col rounded-2xl border-2 p-3.5 shadow-lg',
        'z-10 h-[380px] min-w-[280px] cursor-pointer transition',
        '-translate-y-12 lg:-translate-y-32 lg:first:-translate-y-12 lg:last:-translate-y-12',
        'hover:border-primary/60 hover:scale-105 hover:shadow-xl',
        className,
      )}
      href={`/token/${address}`}
      id={id}
    >
      <div className="flex flex-1 flex-col space-y-2">
        <div className="relative h-[260px] w-full">
          <Image
            fill
            priority
            alt={title}
            blurDataURL="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIj48Y2lyY2xlIGN4PSI1MDAiIGN5PSI1MDAiIHI9IjUwMCIgZmlsbD0iI2ZmZmZmZiIgLz48L3N2Zz4="
            className="rounded-md object-cover"
            sizes="(max-width: 768px) 100vw, (min-width: 769px) 100vw"
            src={imageUrl}
          />
        </div>
        <div>
          <h3 className="min-h-min max-w-[25ch] truncate pb-1 text-lg leading-none font-semibold">
            {title}
          </h3>
          <p className="text-muted-foreground text-xs font-semibold uppercase">
            {currency}
          </p>
        </div>
      </div>
      <div className="flex items-end justify-between">
        <Button variant="icon">
          <MoveRightIcon className="size-5" />
        </Button>
        {emoji && (
          <span aria-label="emoji" className="text-3xl" role="img">
            {emoji}
          </span>
        )}
      </div>
    </Link>
  );
};
