// eslint-disable-next-line simple-import-sort/imports
import { GlobeIcon } from 'lucide-react';
import { useFormatter, useTranslations } from 'next-intl';
import Image from 'next/image';
import type { ComponentPropsWithoutRef } from 'react';

import { GitHubIcon, XIcon } from '@/assets/icons';
import { Address } from '@/components/address';
import { Button } from '@/components/ui/button';
import { InfoBadge } from '@/features/token-detail/components/info-badge';
import { useGetTokenDetail } from '@/hooks/use-get-token-detail';
import { cn } from '@/utils/classnames';
import { formatNumber } from '@/utils/format';

const TOKEN_LINKS = [
  {
    icon: <GitHubIcon />,
    href: 'https://github.com',
  },
  {
    icon: <XIcon />,
    href: 'https://x.com',
  },
  {
    icon: <GlobeIcon />,
    href: 'https://hop.ag',
  },
];

interface HeaderSectionProps extends ComponentPropsWithoutRef<'section'> {
  address: string;
}

export const HeaderSection = ({
  className,
  address,
  ...props
}: HeaderSectionProps) => {
  const token = useGetTokenDetail(address);
  const format = useFormatter();
  const t = useTranslations('token-detail.header-section');

  return (
    <section className={cn('flex w-full flex-col gap-5', className)} {...props}>
      {/* Information about the token goes here */}
      <div className="flex flex-row items-center gap-5">
        <div className="border-primary relative size-[75px] rounded-full border">
          <Image
            fill
            alt={token.name}
            className="aspect-ratio rounded-full object-cover"
            sizes="(max-width: 768px) 75px, (min-width: 769px) 75px"
            src={token.imageUrl}
          />
        </div>
        <div className="flex flex-col">
          <h1 className="text-xl leading-none font-bold tracking-tight uppercase">
            {token.name}
          </h1>
          <div className="flex items-center gap-1">
            <p>{t('created-by')}:</p>{' '}
            <Address address={address} className="text-base" />
          </div>
        </div>
      </div>
      {/* Token market cap */}
      <div className="flex flex-wrap items-center gap-2">
        <InfoBadge
          label={
            <>
              {t('contract-address')}:{' '}
              <Address address={address} className="text-base" />
            </>
          }
        />
        <InfoBadge
          className="text-primary"
          label={
            <>
              {t('market-cap')} : ${formatNumber(token.marketCap || 0, 0)}
            </>
          }
        />
        <InfoBadge
          className="text-muted-foreground"
          label={
            <>
              {t('time-created')}:{' '}
              {format.dateTime(new Date(token.createdAt), {
                year: 'numeric',
                month: 'numeric',
                day: 'numeric',
                hour: 'numeric',
                minute: 'numeric',
                second: 'numeric',
              })}
            </>
          }
        />
        <div className="flex items-center gap-2">
          {TOKEN_LINKS.map((link, index) => (
            <Button
              key={index}
              as="a"
              className="h-10 w-12 rounded-lg px-4"
              href={link.href}
              target="_blank"
              variant="tertiary"
            >
              {link.icon}
            </Button>
          ))}
        </div>
      </div>
    </section>
  );
};
