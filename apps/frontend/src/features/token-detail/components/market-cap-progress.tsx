import { useTranslations } from 'next-intl';
import type { ComponentPropsWithoutRef } from 'react';

import { Progress } from '@/components/ui/progress';
import { cn } from '@/utils/classnames';
import { formatNumber } from '@/utils/format';
import { getCirculatingSupplyPercent } from '@/utils/formula';

interface MarketCapProgressProps extends ComponentPropsWithoutRef<'div'> {
  marketCap: number;
  circulatingSupply: number;
  totalSupply: number;
}

export const MarketCapProgress = ({
  circulatingSupply,
  totalSupply,
  className,
  ...props
}: MarketCapProgressProps) => {
  const percent = getCirculatingSupplyPercent(circulatingSupply, totalSupply);
  const t = useTranslations('token-detail.market-cap-progress');

  return (
    <div className={cn('flex flex-col gap-2', className)} {...props}>
      <div className="flex items-center justify-between text-sm font-medium">
        <p>{t('market-cap-progress')}</p>
        <p className="text-primary">{percent.toFixed(2)}%</p>
      </div>
      <Progress value={percent} />
      <div className="flex items-center justify-between text-xs">
        <p>
          {t('current')}: ${formatNumber(circulatingSupply)}
        </p>
        <p>
          {t('target')}: ${formatNumber(totalSupply)}
        </p>
      </div>
    </div>
  );
};
