'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { WalletIcon } from 'lucide-react';
import { useTranslations } from 'next-intl';
import type { ComponentPropsWithoutRef } from 'react';
import { useForm } from 'react-hook-form';
import z from 'zod';

import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { ToggleGroup, ToggleGroupItem } from '@/components/ui/toggle-group';
import type { State } from '@/features/token-detail/components/buy-sell-form-tabs';
import { cn } from '@/utils/classnames';
import { createNumberInputHandler } from '@/utils/input';

interface BuySellFormProps extends ComponentPropsWithoutRef<'form'> {
  address: string;
  selectedTab?: State;
}

const formSchema = z.object({
  slippage: z.coerce.number().min(0).max(100).default(1),
  amount: z.number().positive().default(0),
});

export const BuySellForm = ({
  className,
  selectedTab,
  ...props
}: BuySellFormProps) => {
  const isBuyTab = selectedTab === 'buy';
  const isSellTab = selectedTab === 'sell';
  const walletAmount = 20; // Example wallet amount, replace with actual logic

  const t = useTranslations('token-detail.buy-sell-form');

  const TOGGLE_ITEMS = [
    { value: 'half', label: t('half') },
    { value: 'max', label: t('max') },
  ];

  const form = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: {
      slippage: 5,
      amount: 0,
    },
  });

  const calculatedAmount = isBuyTab
    ? (form.watch('amount') ?? 0 * 1.2) // Example calculation for buy
    : (form.watch('amount') ?? 0 * 0.8); // Example calculation for sell

  const handleToggleChange = async (value: string) => {
    switch (value) {
      case 'half':
        form.setValue('amount', walletAmount / 2);
        await form.trigger('amount');
        break;
      case 'max':
        form.setValue('amount', walletAmount);
        await form.trigger('amount');
        break;
    }
  };

  const onSubmit = (data: z.infer<typeof formSchema>) => {
    console.log('Form submitted with data:', data);
    // Handle form submission logic here
  };

  return (
    <Form {...form}>
      <form
        className={cn('flex flex-col gap-4', className)}
        {...props}
        onSubmit={form.handleSubmit(onSubmit)}
      >
        <FormField
          control={form.control}
          name="slippage"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t('slippage')} (%)</FormLabel>
              <FormControl>
                <Input
                  className={cn({
                    'focus-visible:!border-emerald-400/80 focus-visible:!ring-emerald-400/50':
                      isBuyTab,
                    'focus-visible:!border-red-400/80 focus-visible:!ring-red-400/50':
                      isSellTab,
                  })}
                  type="number"
                  {...field}
                  value={
                    field.value !== undefined && field.value !== null
                      ? // eslint-disable-next-line @typescript-eslint/no-base-to-string
                        String(field.value)
                      : ''
                  }
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="amount"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t('amount')}</FormLabel>
              <FormControl>
                <Input
                  className={cn({
                    'focus-visible:!border-emerald-400/80 focus-visible:!ring-emerald-400/50':
                      isBuyTab,
                    'focus-visible:!border-red-400/80 focus-visible:!ring-red-400/50':
                      isSellTab,
                  })}
                  rightContent={isBuyTab ? 'ETH' : 'USD'}
                  type="number"
                  {...field}
                  onChange={createNumberInputHandler(field)}
                />
              </FormControl>
              <FormMessage />
              <FormDescription>
                ≈ {calculatedAmount} {isBuyTab ? 'USD' : 'ETH'}
              </FormDescription>
            </FormItem>
          )}
        />
        {/* Current wallet amount */}
        <div
          className={cn('flex items-center gap-1', {
            'text-emerald-400': isBuyTab,
            'text-red-400': isSellTab,
          })}
        >
          <WalletIcon className="size-4" />
          <p className="font-medium">
            {walletAmount} {isBuyTab ? 'ETH' : 'USD'}
          </p>
        </div>
        <ToggleGroup className="flex-wrap gap-2" size="sm" type="single">
          {TOGGLE_ITEMS.map((item) => (
            <ToggleGroupItem
              key={item.value}
              className="h-6 cursor-pointer rounded-full px-3 text-xs font-medium capitalize"
              data-state="off" // Disabled the toggle state for now
              value={item.value}
              variant="blue"
              onClick={() => handleToggleChange(item.value)}
            >
              {item.label}
            </ToggleGroupItem>
          ))}
        </ToggleGroup>
        <Button
          className={cn({
            'bg-emerald-500 hover:bg-emerald-600': isBuyTab,
            'bg-red-500 hover:bg-red-600': isSellTab,
          })}
          disabled={!form.formState.isValid}
          type="submit"
        >
          {isBuyTab ? `${t('buy')} ETH` : `${t('sell')} USD`}
        </Button>
      </form>
    </Form>
  );
};
