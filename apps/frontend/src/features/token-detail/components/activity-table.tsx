import { ChartColumnIcon } from 'lucide-react';
import { useFormatter, useNow, useTranslations } from 'next-intl';
import type { ComponentPropsWithoutRef } from 'react';

import { Address } from '@/components/address';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { TabSectionTitle } from '@/features/token-detail/components/tab-section-title';
import { ACTIVITY_MOCK_DATA } from '@/features/token-detail/constants/activity-mock-data';
import { cn } from '@/utils/classnames';
import { formatDynamicNumber, formatNumber } from '@/utils/format';

interface ActivityTableProps extends ComponentPropsWithoutRef<'table'> {
  address: string;
}

export const ActivityTable = ({ className, ...props }: ActivityTableProps) => {
  const now = useNow();
  const format = useFormatter();
  const t = useTranslations('token-detail.activity-table');

  return (
    <div className="flex flex-col gap-4">
      <TabSectionTitle icon={<ChartColumnIcon />} title={t('title')} />
      <Table className={cn('w-full table-auto', className)} {...props}>
        <TableHeader>
          <TableRow>
            <TableHead>{t('date')}</TableHead>
            <TableHead>{t('type')}</TableHead>
            <TableHead>{t('price')}</TableHead>
            <TableHead>{t('mini')}</TableHead>
            <TableHead>{t('account')}</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {ACTIVITY_MOCK_DATA.map((activity) => (
            <TableRow key={activity.id}>
              <TableCell className="text-muted-foreground">
                {format.relativeTime(new Date(activity.createdAt), now)}
              </TableCell>
              <TableCell>
                <Badge className="capitalize" variant={activity.type}>
                  {activity.type}
                </Badge>
              </TableCell>
              <TableCell>
                {formatDynamicNumber(Number(activity.price), 6)}
              </TableCell>
              <TableCell>{formatNumber(Number(activity.mini), 0)}</TableCell>
              <TableCell>
                <Address
                  address={activity.account}
                  enableCopy={false}
                  redirectUrl={`https://explorer.hop.exchange/address/${activity.account}`}
                />
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
};
