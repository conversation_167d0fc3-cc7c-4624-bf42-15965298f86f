'use client';

import { useTranslations } from 'next-intl';
import { useState } from 'react';

import type { TabsVariants } from '@/components/ui/tabs';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { BuySellForm } from '@/features/token-detail/components/buy-sell-form';

export type State = 'buy' | 'sell';

interface BuySellFormTabsProps {
  address: string;
}

export const BuySellFormTabs = ({
  address,
  ...props
}: BuySellFormTabsProps) => {
  const [selectedTab, setSelectedTab] = useState<State>('buy');

  const t = useTranslations('token-detail.buy-sell-form');

  const TAB_ITEMS = [
    {
      value: 'buy',
      label: t('buy'),
    },
    {
      value: 'sell',
      label: t('sell'),
    },
  ];

  const handleTabChange = (value: string) => {
    setSelectedTab(value as State);
  };

  return (
    <Tabs
      className="gap-4"
      value={selectedTab}
      onValueChange={handleTabChange}
      {...props}
    >
      <TabsList className="w-full gap-3 px-0">
        {TAB_ITEMS.map((item) => (
          <TabsTrigger
            key={item.value}
            size="lg"
            value={item.value}
            variant={item.value as TabsVariants['variant']}
          >
            {item.label}
          </TabsTrigger>
        ))}
      </TabsList>
      <TabsContent value="buy">
        <BuySellForm address={address} selectedTab={selectedTab} />
      </TabsContent>
      <TabsContent value="sell">
        <BuySellForm address={address} selectedTab={selectedTab} />
      </TabsContent>
    </Tabs>
  );
};
