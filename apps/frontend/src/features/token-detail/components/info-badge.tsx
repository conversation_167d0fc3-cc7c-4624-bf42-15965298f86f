import type { ComponentPropsWithoutRef, ReactNode } from 'react';

import { cn } from '@/utils/classnames';

interface InfoBadgeProps extends ComponentPropsWithoutRef<'div'> {
  icon?: ReactNode;
  label: ReactNode;
  value?: ReactNode;
}

export const InfoBadge = ({
  className,
  label,
  value,
  icon,
  ...props
}: InfoBadgeProps) => {
  return (
    <div
      className={cn(
        'bg-card text-card-foreground flex w-fit items-center justify-between gap-1 rounded-lg px-4 py-2 font-medium',
        "[&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",
        className,
      )}
      {...props}
    >
      <span className="flex items-center gap-2">
        {icon}
        {label}
      </span>
      {value}
    </div>
  );
};
