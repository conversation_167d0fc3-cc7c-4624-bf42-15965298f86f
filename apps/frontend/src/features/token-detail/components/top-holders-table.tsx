import { TrophyIcon } from 'lucide-react';
import { useTranslations } from 'next-intl';
import type { ComponentPropsWithoutRef } from 'react';

import { Address } from '@/components/address';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { TabSectionTitle } from '@/features/token-detail/components/tab-section-title';
import { TOP_HOLDERS_MOCK_DATA } from '@/features/token-detail/constants/top-holders-mock-data';
import { cn } from '@/utils/classnames';

interface TopHoldersTableProps extends ComponentPropsWithoutRef<'table'> {
  address: string;
}

export const TopHoldersTable = ({
  className,
  ...props
}: TopHoldersTableProps) => {
  const t = useTranslations('token-detail.top-holders-table');

  return (
    <div className="flex flex-col gap-4">
      <TabSectionTitle icon={<TrophyIcon />} title={t('title')} />
      <Table className={cn('w-full table-auto', className)} {...props}>
        <TableHeader>
          <TableRow>
            <TableHead>#</TableHead>
            <TableHead>{t('holder')}</TableHead>
            <TableHead>{t('percentage')}</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {TOP_HOLDERS_MOCK_DATA.map((holder, index) => (
            <TableRow key={holder.address}>
              <TableCell>{index + 1}</TableCell>
              <TableCell>
                <Address address={holder.address} />
              </TableCell>
              <TableCell>{holder.percentage}</TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
};
