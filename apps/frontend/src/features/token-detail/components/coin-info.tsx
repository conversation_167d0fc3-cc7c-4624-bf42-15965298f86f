import {
  CalendarIcon,
  CircleQuestionMarkIcon,
  DatabaseIcon,
  InfoIcon,
  LinkIcon,
  PercentIcon,
  UserIcon,
} from 'lucide-react';
import { useFormatter, useTranslations } from 'next-intl';
import type { ComponentPropsWithoutRef } from 'react';

import { Address } from '@/components/address';
import { InfoBadge } from '@/features/token-detail/components/info-badge';
import { TabSectionTitle } from '@/features/token-detail/components/tab-section-title';
import type { Token } from '@/types/token';
import { cn } from '@/utils/classnames';
import { formatNumber } from '@/utils/format';

interface CoinInfoProps extends ComponentPropsWithoutRef<'div'> {
  token: Token;
}

export const CoinInfo = ({ token, className, ...props }: CoinInfoProps) => {
  const format = useFormatter();
  const t = useTranslations('token-detail.coin-info');

  const COIN_INFO = [
    {
      icon: <DatabaseIcon />,
      label: t('supply'),
      value: `$${formatNumber(token.totalSupply)}`,
    },
    {
      icon: <CalendarIcon />,
      label: t('created'),
      value: format.dateTime(new Date(token.createdAt), {
        year: 'numeric',
        month: 'numeric',
        day: 'numeric',
        hour: 'numeric',
        minute: 'numeric',
        second: 'numeric',
      }),
    },
    {
      icon: <PercentIcon />,
      label: t('trade-fees'),
      value: (
        <span className="flex items-center gap-1">
          {token.tradeFee}% <CircleQuestionMarkIcon />
        </span>
      ),
    },
    {
      icon: <LinkIcon />,
      label: t('contract-address'),
      value: <Address address={token.address} />,
    },
    {
      icon: <UserIcon />,
      label: t('developer-address'),
      value: <Address address={token.address} />,
    },
  ];

  return (
    <div className={cn('flex flex-col gap-4', className)} {...props}>
      <div className="flex flex-col gap-2">
        <TabSectionTitle icon={<InfoIcon />} title={t('about-coin')} />
        <div className="bg-card text-card-foreground flex w-fit items-center justify-between gap-1 rounded-lg p-4">
          {token.description || 'No description available.'}
        </div>
      </div>
      {COIN_INFO.map((info, index) => (
        <InfoBadge
          key={index}
          className="w-full p-4"
          icon={info.icon}
          label={info.label}
          value={info.value}
        />
      ))}
    </div>
  );
};
