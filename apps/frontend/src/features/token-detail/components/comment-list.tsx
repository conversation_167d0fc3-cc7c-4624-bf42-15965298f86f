import { MessageSquarePlusIcon } from 'lucide-react';
import { useTranslations } from 'next-intl';
import type { ComponentPropsWithoutRef } from 'react';

import { Button } from '@/components/ui/button';
import { CommentCard } from '@/features/token-detail/components/comment-card';
import type { TokenComment } from '@/types/token';
import { cn } from '@/utils/classnames';

interface CommentListProps extends ComponentPropsWithoutRef<'div'> {
  comments: TokenComment[];
}

export const CommentList = ({
  className,
  comments,
  ...props
}: CommentListProps) => {
  const t = useTranslations('token-detail.comment-list');

  return (
    <div className={cn('flex flex-col gap-4', className)} {...props}>
      <Button>
        <MessageSquarePlusIcon /> {t('add-reply')}
      </Button>
      <div className="flex max-h-[600px] flex-col gap-4 overflow-y-auto">
        {comments.length > 0 ? (
          comments.map((comment) => (
            <CommentCard key={comment.id} comment={comment} />
          ))
        ) : (
          <p className="text-muted-foreground text-center">No comments yet.</p>
        )}
      </div>
    </div>
  );
};
