import type { TokenHolder } from '@/types/token';

// Generate random percentages that sum to 100%
const generatePercentages = (count: number): number[] => {
  // Generate random numbers
  const randoms = Array.from({ length: count }, () => Math.random());

  // Calculate the sum
  const sum = randoms.reduce((acc, val) => acc + val, 0);

  // Normalize to sum to 100 and round to 2 decimal places
  const percentages = randoms.map((val) =>
    parseFloat(((val / sum) * 100).toFixed(2)),
  );

  // Adjust the last value to ensure exact sum of 100%
  const currentSum = percentages.reduce((acc, val) => acc + val, 0);
  percentages[percentages.length - 1] = parseFloat(
    (100 - (currentSum - percentages[percentages.length - 1])).toFixed(2),
  );

  return percentages;
};

const percentages = generatePercentages(10);

export const TOP_HOLDERS_MOCK_DATA: TokenHolder[] = Array.from(
  { length: 10 },
  (_, index) => ({
    address: `0x${Math.random().toString(16).slice(2, 10)}`,
    balance: Math.floor(Math.random() * (1000000 - 1000 + 1)) + 1000, // Random balance between 1000 and 1000000
    percentage: percentages[index],
  }),
).sort((a, b) => b.percentage - a.percentage);
