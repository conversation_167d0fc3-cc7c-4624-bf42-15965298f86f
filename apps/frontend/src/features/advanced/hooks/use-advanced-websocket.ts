import { useCallback, useEffect, useRef, useState } from 'react';

import {
  getMockDataForType,
  getUpdatedMockData,
} from '@/features/advanced/constants/advanced-token-mock-data';
import type { Token } from '@/types/token';

export type AdvancedListType =
  | 'newly-created'
  | 'graduating'
  | 'graduated'
  | 'watchlist';

interface UseAdvancedWebSocketProps {
  type: AdvancedListType;
  enabled?: boolean;
  useMockData?: boolean; // Add flag to use mock data
}

export const useAdvancedWebSocket = ({
  type,
  enabled = true,
  useMockData = true, // Default to true for now
}: UseAdvancedWebSocketProps) => {
  const [data, setData] = useState<Token[]>([]);
  const [isConnected, setIsConnected] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const wsRef = useRef<WebSocket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout>(undefined);
  const intervalRef = useRef<NodeJS.Timeout>(undefined);
  const reconnectAttempts = useRef(0);
  const maxReconnectAttempts = 5;

  const getWebSocketUrl = useCallback((type: AdvancedListType) => {
    const baseUrl = process.env.NEXT_PUBLIC_WS_URL ?? 'ws://localhost:8080';
    return `${baseUrl}/advanced/${type}`;
  }, []);

  // Mock data simulation
  const startMockDataSimulation = useCallback(() => {
    if (!enabled || isPaused) return;

    setIsLoading(true);
    setError(null);

    // Simulate initial loading
    setTimeout(() => {
      setData(getMockDataForType(type));
      setIsConnected(true);
      setIsLoading(false);
    }, 1000);

    // Simulate real-time updates every 3 seconds
    intervalRef.current = setInterval(() => {
      // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition
      if (!isPaused) {
        setData(getUpdatedMockData(type));
      }
    }, 3000);
  }, [type, enabled, isPaused]);

  const stopMockDataSimulation = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = undefined;
    }
    setIsConnected(false);
  }, []);

  const connect = useCallback(() => {
    if (!enabled || isPaused) return;

    if (useMockData) {
      startMockDataSimulation();
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      const url = getWebSocketUrl(type);
      wsRef.current = new WebSocket(url);

      wsRef.current.onopen = () => {
        setIsConnected(true);
        setIsLoading(false);
        setError(null);
        reconnectAttempts.current = 0;
      };

      wsRef.current.onmessage = (event) => {
        try {
          // eslint-disable-next-line @typescript-eslint/no-unsafe-argument
          const newData = JSON.parse(event.data);
          // eslint-disable-next-line @typescript-eslint/no-unsafe-argument
          setData(newData);
        } catch (parseError) {
          console.error(`Error parsing ${type} data:`, parseError);
          setError('Failed to parse data');
        }
      };

      wsRef.current.onclose = (event) => {
        setIsConnected(false);
        setIsLoading(false);

        if (
          !event.wasClean &&
          reconnectAttempts.current < maxReconnectAttempts
        ) {
          const delay = Math.min(
            1000 * Math.pow(2, reconnectAttempts.current),
            30000,
          );
          reconnectTimeoutRef.current = setTimeout(() => {
            reconnectAttempts.current++;
            connect();
          }, delay);
        }
      };

      wsRef.current.onerror = (error) => {
        console.error(`WebSocket error for ${type}:`, error);
        setError(`Connection failed for ${type}`);
        setIsLoading(false);
      };
    } catch {
      setError(`Failed to connect to ${type}`);
      setIsLoading(false);
    }
  }, [
    type,
    enabled,
    isPaused,
    useMockData,
    getWebSocketUrl,
    startMockDataSimulation,
  ]);

  const disconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
    }

    if (useMockData) {
      stopMockDataSimulation();
    } else {
      if (wsRef.current) {
        wsRef.current.close();
        wsRef.current = null;
      }
    }

    setIsConnected(false);
    setData([]);
  }, [useMockData, stopMockDataSimulation]);

  const pause = useCallback(() => {
    setIsPaused(true);
    if (useMockData) {
      stopMockDataSimulation();
    } else {
      disconnect();
    }
  }, [useMockData, disconnect, stopMockDataSimulation]);

  const resume = useCallback(() => {
    setIsPaused(false);
    connect();
  }, [connect]);

  const retry = useCallback(() => {
    setError(null);
    reconnectAttempts.current = 0;
    connect();
  }, [connect]);

  useEffect(() => {
    connect();
    return disconnect;
  }, [connect, disconnect]);

  return {
    data,
    isConnected,
    isPaused,
    error,
    isLoading,
    pause,
    resume,
    retry,
  };
};
