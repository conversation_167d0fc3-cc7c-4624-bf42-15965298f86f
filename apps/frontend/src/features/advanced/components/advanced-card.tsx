import { ExternalLinkIcon, StarIcon, TimerIcon } from 'lucide-react';
import Image from 'next/image';
import { useFormatter, useNow, useTranslations } from 'next-intl';
import type { ComponentPropsWithoutRef } from 'react';

import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { cn } from '@/utils/classnames';
import { formatMarketCap } from '@/utils/currency';
import { getCirculatingSupplyPercent } from '@/utils/formula';

export interface AdvancedToken {
  name: string;
  imageUrl: string;
  symbol: string;
  marketCap: number;
  circulatingSupply: number;
  totalSupply: number;
  createdAt: string; // ISO date string
}

export interface AdvancedCardProps extends ComponentPropsWithoutRef<'div'> {
  token: AdvancedToken;
  amount: number;
}

export const AdvancedCard = ({
  token,
  amount,
  className,
  ...props
}: AdvancedCardProps) => {
  const {
    name,
    imageUrl,
    symbol,
    marketCap,
    circulatingSupply,
    totalSupply,
    createdAt,
  } = token;

  const t = useTranslations('advanced.advanced-card');
  const now = useNow();
  const format = useFormatter();
  const progress = getCirculatingSupplyPercent(circulatingSupply, totalSupply);

  return (
    <div
      className={cn(
        'flex flex-col gap-2 rounded-md p-2',
        'bg-card w-80 border',
        className,
      )}
      {...props}
    >
      {/* Logo and Name */}
      <div className="flex items-center justify-between">
        {/* Logo and Name – left side */}
        <div className="flex items-center gap-2">
          <div className="border-primary relative size-7.5 shrink-0 rounded-full border-[0.5px] p-0.5">
            <Image
              fill
              alt={`${name} logo`}
              className="rounded-full object-cover"
              src={imageUrl}
            />
          </div>
          {/* Name and Symbol */}
          <div>
            <h2 className="text-xs font-bold">{name}</h2>
            <div className="text-muted-foreground text-2xs flex items-center gap-2">
              <p>${symbol}</p>
              <Tooltip>
                <TooltipTrigger asChild>
                  <span className="flex items-center gap-0.5">
                    <TimerIcon className="size-2" />
                    {format.relativeTime(new Date(createdAt), now)}
                  </span>
                </TooltipTrigger>
                <TooltipContent>
                  {format.dateTime(new Date(createdAt), {
                    year: 'numeric',
                    month: 'numeric',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit',
                  })}
                </TooltipContent>
              </Tooltip>
            </div>
          </div>
        </div>
        {/* Link and Favorite – right side */}
        <div className="flex items-center gap-1">
          <Button
            className="bg-tertiary hover:bg-tertiary/80 size-4 cursor-pointer rounded-full p-0.5"
            size="sm"
            variant="icon"
          >
            <ExternalLinkIcon className="size-3" />
          </Button>
          <Button
            className="bg-tertiary hover:bg-tertiary/80 size-4 cursor-pointer rounded-full p-0.5"
            size="sm"
            variant="icon"
          >
            <StarIcon className="size-3" />
          </Button>
        </div>
      </div>
      {/* Market cap and add button */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-1">
          <p className="text-xs font-medium">MC:</p>
          <p className="text-xs font-bold">{formatMarketCap(marketCap)}</p>
        </div>
        <Button size="xs">+ {amount} SOL</Button>
      </div>
      {/* Progress bar */}
      <div className="flex flex-col gap-1">
        <div className="text-muted-foreground flex items-center justify-between text-xs">
          <p>{t('progress')}</p>
          <p className="font-medium">{progress.toFixed(2)}%</p>
        </div>
        <Progress value={progress} />
      </div>
    </div>
  );
};
