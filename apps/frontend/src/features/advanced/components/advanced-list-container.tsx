import {
  AlertCircleIcon,
  PauseIcon,
  PlayIcon,
  RefreshCwIcon,
} from 'lucide-react';
import type { ComponentPropsWithoutRef } from 'react';

import { Button } from '@/components/ui/button';
import { AdvancedList } from '@/features/advanced/components/advanced-list';
import type { AdvancedListType } from '@/features/advanced/hooks/use-advanced-websocket';
import { useAdvancedWebSocket } from '@/features/advanced/hooks/use-advanced-websocket';
import { cn } from '@/utils/classnames';

interface AdvancedListContainerProps extends ComponentPropsWithoutRef<'div'> {
  type: AdvancedListType;
  title: string;
  amount: number;
  enabled?: boolean;
}

export const AdvancedListContainer = ({
  type,
  title,
  amount,
  enabled = true,
  className,
  ...props
}: AdvancedListContainerProps) => {
  const {
    data,
    isConnected,
    isPaused,
    error,
    isLoading,
    pause,
    resume,
    retry,
  } = useAdvancedWebSocket({ type, enabled });

  return (
    <div className={cn('flex w-80 flex-col gap-3', className)} {...props}>
      {/* Header with title and controls */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <h3 className="font-semibold">{title}</h3>
          <div className="flex items-center gap-1">
            <div
              className={cn(
                'h-2 w-2 rounded-full',
                isConnected ? 'animate-pulse bg-green-500' : 'bg-red-500',
              )}
            />
          </div>
        </div>

        <div className="flex items-center gap-2">
          {error && (
            <Button
              className="text-destructive"
              size="sm"
              variant="secondary"
              onClick={retry}
            >
              <RefreshCwIcon />
              Retry
            </Button>
          )}

          <Button
            disabled={isLoading}
            size="sm"
            variant="icon"
            onClick={isPaused ? resume : pause}
          >
            {isPaused ? <PlayIcon /> : <PauseIcon />}
          </Button>
        </div>
      </div>

      {/* Error message */}
      {error && (
        <div className="bg-destructive/10 border-destructive/20 flex items-center gap-2 rounded-md border p-3">
          <AlertCircleIcon className="text-destructive h-4 w-4" />
          <span className="text-destructive text-sm">{error}</span>
        </div>
      )}

      {/* Loading state */}
      {isLoading && (
        <div className="flex items-center justify-center p-8">
          <RefreshCwIcon className="size-6 animate-spin" />
          <span className="ml-2">Connecting...</span>
        </div>
      )}

      {/* List content */}
      {!isLoading && (
        <AdvancedList
          amount={amount}
          className={cn(error && 'opacity-50')}
          items={data}
        />
      )}
    </div>
  );
};
