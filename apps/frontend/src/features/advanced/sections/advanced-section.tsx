'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { useTranslations } from 'next-intl';
import type { ComponentPropsWithoutRef } from 'react';
import { useForm } from 'react-hook-form';
import z from 'zod';

import { SolIcon } from '@/assets/icons';
import { Form, FormField } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { AdvancedListContainer } from '@/features/advanced/components/advanced-list-container';
import { cn } from '@/utils/classnames';
import { createNumberInputHandler } from '@/utils/input';

interface AdvancedSectionProps extends ComponentPropsWithoutRef<'section'> {}

const formSchema = z.object({
  amount: z.number().positive().default(0.01),
});

export const AdvancedSection = ({
  className,
  ...props
}: AdvancedSectionProps) => {
  const form = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: {
      amount: 0.01,
    },
  });

  const amount = form.watch('amount') ?? 0.01;
  const t = useTranslations('advanced.advanced-section');

  const ADVANCED_LISTS = [
    { type: 'newly-created' as const, title: t('newly-created') },
    { type: 'graduating' as const, title: t('graduating') },
    { type: 'graduated' as const, title: t('graduated') },
    { type: 'watchlist' as const, title: t('watchlist') },
  ];

  return (
    <section
      className={cn(
        'flex w-full flex-col items-center gap-6 px-4 py-10 md:px-0 md:py-12',
        className,
      )}
      {...props}
    >
      {/* Amount form */}
      <Form {...form}>
        <form className="flex w-full max-w-28 items-center justify-center px-2">
          <FormField
            control={form.control}
            name="amount"
            render={({ field }) => (
              <Input
                className="w-full"
                placeholder="0,01"
                rightContent={<SolIcon />}
                type="number"
                {...field}
                onChange={createNumberInputHandler(field)}
              />
            )}
          />
        </form>
      </Form>
      {/* List */}
      {/* <div className="flex w-full items-start justify-center gap-6 overflow-x-auto"> */}
      <div className="w-full overflow-x-auto">
        <div className="flex min-w-max items-start justify-center gap-6">
          {ADVANCED_LISTS.map(({ type, title }) => (
            <AdvancedListContainer
              key={type}
              amount={amount}
              title={title}
              type={type}
            />
          ))}
        </div>
      </div>
      {/* </div> */}
    </section>
  );
};
