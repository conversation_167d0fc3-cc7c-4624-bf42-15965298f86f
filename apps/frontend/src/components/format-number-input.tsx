import { forwardRef, useEffect, useState } from 'react';

import { Input } from '@/components/ui/input';
import { formatNumber } from '@/utils/format';

interface FormattedNumberInputProps
  extends Omit<React.ComponentProps<typeof Input>, 'onChange' | 'value'> {
  value?: number;
  onChange?: (value: number | undefined) => void;
}

const FormattedNumberInput = forwardRef<
  HTMLInputElement,
  FormattedNumberInputProps
>(({ value, onChange, ...props }, ref) => {
  const [displayValue, setDisplayValue] = useState(
    value ? formatNumber(value, 0) : '',
  );

  // Sync displayValue with external value changes
  useEffect(() => {
    setDisplayValue(value ? formatNumber(value, 0) : '');
  }, [value]);

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = event.target.value.replace(/,/g, ''); // Remove commas

    // Only allow integers (no decimals)
    if (inputValue === '' || /^\d+$/.test(inputValue)) {
      if (inputValue === '') {
        setDisplayValue('');
        onChange?.(undefined);
      } else {
        const numericValue = parseInt(inputValue, 10);
        setDisplayValue(formatNumber(numericValue, 0));
        onChange?.(numericValue);
      }
    }
  };

  return (
    <Input
      {...props}
      ref={ref}
      type="text"
      value={displayValue}
      onChange={handleChange}
    />
  );
});

FormattedNumberInput.displayName = 'FormattedNumberInput';

export { FormattedNumberInput };
