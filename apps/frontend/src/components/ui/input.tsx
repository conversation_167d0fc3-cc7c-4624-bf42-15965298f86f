import type { ComponentPropsWithoutRef, ReactNode } from 'react';
import { forwardRef } from 'react';

import { cn } from '@/utils/classnames';

interface InputProps extends ComponentPropsWithoutRef<'input'> {
  leftContent?: ReactNode;
  rightContent?: ReactNode;
  fullWidth?: boolean;
}

const Input = forwardRef<HTMLInputElement, InputProps>(
  (
    { className, type, leftContent, rightContent, fullWidth, ...props },
    ref,
  ) => {
    return (
      <div
        className={cn(
          'relative flex items-center',
          "[&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",
          fullWidth ? 'w-full' : 'w-auto',
        )}
      >
        {leftContent && (
          <span className="absolute top-1/2 left-3 -translate-y-1/2">
            {leftContent}
          </span>
        )}
        <input
          ref={ref}
          className={cn(
            'flex h-9 w-full min-w-0 rounded-md py-1 text-base shadow-xs outline-none md:text-sm',
            'border-input bg-secondary border',
            'placeholder:text-muted-foreground transition-[color,box-shadow]',
            'file:text-foreground file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium',
            'disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50',
            'selection:bg-primary selection:text-primary-foreground',
            'focus-visible:border-primary focus-visible:ring-primary/50 focus-visible:ring-[2px]',
            'aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive',
            leftContent ? 'pl-10' : 'pl-3',
            rightContent ? 'pr-10' : 'pr-3',
            className,
          )}
          data-slot="input"
          type={type}
          {...props}
        />
        {rightContent && (
          <span className="absolute top-1/2 right-3 -translate-y-1/2">
            {rightContent}
          </span>
        )}
      </div>
    );
  },
);

Input.displayName = 'Input';

export { Input };
