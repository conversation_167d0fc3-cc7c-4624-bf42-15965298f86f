'use client';

import * as TogglePrimitive from '@radix-ui/react-toggle';
import * as React from 'react';
import type { VariantProps } from 'tailwind-variants';
import { tv } from 'tailwind-variants';

import { cn } from '@/utils/classnames';

const toggleVariants = tv({
  base: [
    'inline-flex items-center justify-center gap-2 rounded-md',
    'text-sm font-semibold whitespace-nowrap transition outline-none',
    'hover:bg-muted hover:text-muted-foreground',
    'disabled:pointer-events-none disabled:opacity-50',
    'aria-invalid:ring-destructive/20 aria-invalid:border-destructive',
    'data-[state=on]:bg-accent data-[state=on]:text-accent-foreground',
    'focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]',
    "[&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",
  ],
  variants: {
    variant: {
      default: [
        'bg-secondary text-secondary-foreground',
        'hover:text-secondary-foreground hover:bg-secondary/40',
        'data-[state=on]:bg-primary data-[state=on]:text-primary-foreground',
      ],
      outline:
        'border-input hover:bg-accent hover:text-accent-foreground border bg-transparent shadow-xs',
      blue: [
        'bg-blue-500/20 text-blue-700',
        'hover:bg-blue-500 hover:text-white',
        'data-[state=on]:bg-blue-500 data-[state=on]:text-white',
      ],
      yellow: [
        'bg-secondary text-secondary-foreground',
        'hover:bg-tertiary hover:text-secondary-foreground',
        'data-[state=on]:bg-yellow data-[state=on]:text-background',
      ],
    },
    size: {
      default: 'h-9 min-w-9 px-1',
      sm: 'h-[30px] gap-1 px-3 py-1',
      lg: 'h-10 min-w-10 px-4 py-2.5',
    },
  },
  defaultVariants: {
    variant: 'default',
    size: 'default',
  },
});

function Toggle({
  className,
  variant,
  size,
  ...props
}: React.ComponentProps<typeof TogglePrimitive.Root> &
  VariantProps<typeof toggleVariants>) {
  return (
    <TogglePrimitive.Root
      className={cn(toggleVariants({ variant, size, className }))}
      data-slot="toggle"
      {...props}
    />
  );
}

export { Toggle, toggleVariants };
