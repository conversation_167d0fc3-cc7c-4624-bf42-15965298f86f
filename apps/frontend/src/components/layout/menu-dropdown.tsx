import {
  BoxIcon,
  HandCoinsIcon,
  MenuIcon,
  RocketIcon,
  TrophyIcon,
} from 'lucide-react';
import type { ComponentProps } from 'react';

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

interface MenuDropdownProps extends ComponentProps<'button'> {}

const MENU_ITEMS = [
  { icon: HandCoinsIcon, label: 'Revenue' },
  { icon: TrophyIcon, label: 'Creator Rewards' },
  { icon: RocketIcon, label: 'Tech Launch' },
  { icon: BoxIcon, label: 'Box' },
];

export const MenuDropdown = ({ className, ...props }: MenuDropdownProps) => {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger className={className} size="sm" {...props}>
        <MenuIcon /> <span className="hidden md:block">Menu</span>
      </DropdownMenuTrigger>
      <DropdownMenuContent>
        {MENU_ITEMS.map(({ icon: Icon, label }) => (
          <DropdownMenuItem key={label}>
            <Icon />
            {label}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};
