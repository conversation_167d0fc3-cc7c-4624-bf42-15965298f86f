import { ChartNoAxesColumnIcon } from 'lucide-react';
import { useTranslations } from 'next-intl';
import type { ComponentPropsWithoutRef } from 'react';
import { useMemo } from 'react';

import { LanguageSwitcher } from '@/components/language-switcher';
import { LaunchTokenButton } from '@/components/launch-token-button';
import { Logo } from '@/components/logo';
import { Button } from '@/components/ui/button';
import { WalletConnectButton } from '@/components/wallet-connect-button';
import { cn } from '@/utils/classnames';

type HeaderProps = ComponentPropsWithoutRef<'header'>;

export const Header = ({ className, ...props }: HeaderProps) => {
  const t = useTranslations('header.menu');

  const MENU_ITEMS = useMemo(
    () => [
      {
        icon: ChartNoAxesColumnIcon,
        label: t('advanced'),
        href: '/advanced',
      },
    ],
    [t],
  );

  return (
    <header
      className={cn(
        'border-muted-foreground/25 flex min-h-15 w-full flex-col items-center justify-between gap-3 border-b py-3 md:flex-row md:py-5',
        className,
      )}
      {...props}
    >
      <div className="flex w-full flex-col items-center justify-between gap-3 px-6 md:flex-row xl:px-24">
        <Logo size={40} />
        <nav className="flex items-center gap-3">
          <LanguageSwitcher />
          {MENU_ITEMS.map(({ icon: Icon, label, href }) => (
            <Button key={label} as="a" href={href} size="sm">
              <Icon /> <span className="hidden md:block">{label}</span>
            </Button>
          ))}
          <WalletConnectButton />
          <LaunchTokenButton />
          {/* // TODO: We disable the menu dropdown for now */}
          {/* <MenuDropdown /> */}
        </nav>
      </div>
    </header>
  );
};
