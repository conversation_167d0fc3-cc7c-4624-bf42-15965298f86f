import type { ComponentPropsWithoutRef } from 'react';

import { Background } from '@/components/layout/background';
import { Footer } from '@/components/layout/footer';
import { Header } from '@/components/layout/header';
import { NetworkDiagnostic } from '@/components/network-diagnostic';
import { cn } from '@/utils/classnames';

export interface MainLayoutProps extends ComponentPropsWithoutRef<'div'> {
  showBackground?: boolean;
}

export const MainLayout = ({
  className,
  children,
  showBackground = true,
  ...props
}: MainLayoutProps) => {
  return (
    <div
      className={cn('relative flex min-h-screen w-full flex-col', className)}
      {...props}
    >
      {showBackground && <Background />}
      <Header className="z-10" />
      <div className="relative z-10 flex grow flex-col px-0 md:px-6">
        {children}
      </div>
      {/* Network diagnostic for debugging ZKLogin issues */}
      <div className="fixed right-4 bottom-4 z-50">
        <NetworkDiagnostic />
      </div>
      <Footer className="z-10" />
    </div>
  );
};
