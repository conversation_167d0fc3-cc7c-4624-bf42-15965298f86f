import { useTranslations } from 'next-intl';
import type { ComponentPropsWithoutRef } from 'react';
import { useMemo } from 'react';

import { GitHubIcon, XIcon } from '@/assets/icons';
import { Button } from '@/components/ui/button';
import { cn } from '@/utils/classnames';

interface FooterProps extends ComponentPropsWithoutRef<'footer'> {}

export const Footer = ({ className, ...props }: FooterProps) => {
  const currentYear = new Date().getFullYear();
  const t = useTranslations('footer.menu');

  const MENU_ITEMS = useMemo(
    () => [
      { label: t('request-api'), href: '/request-api' },
      { label: t('verify-token'), href: '/verify-token' },
      { label: t('docs'), href: '/docs' },
    ],
    [t],
  );

  return (
    <footer
      className={cn(
        'mx-auto flex min-h-10 w-full max-w-[1400px] items-center justify-between p-2',
        className,
      )}
      {...props}
    >
      <span className="text-xs font-semibold">© {currentYear} hop.fun</span>
      <nav className="flex items-center gap-3">
        {MENU_ITEMS.map((item) => (
          <Button key={item.label} href={item.href} variant="link">
            {item.label}
          </Button>
        ))}
        <Button as="a" href="https://x.com" variant="icon">
          <XIcon className="size-3.5" />
        </Button>
        <Button as="a" href="https://github.com" variant="icon">
          <GitHubIcon className="size-3.5" />
        </Button>
      </nav>
    </footer>
  );
};
