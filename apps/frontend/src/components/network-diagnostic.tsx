'use client';

import { useSuiClient } from '@mysten/dapp-kit';
import { useEffect, useState } from 'react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { networkConfigService } from '@/services/network-config.service';

interface NetworkInfo {
  appNetwork: string;
  appRpcUrl: string;
  currentEpoch?: string;
  chainIdentifier?: string;
  contractsConfigured: boolean;
  error?: string;
}

export function NetworkDiagnostic() {
  const [networkInfo, setNetworkInfo] = useState<NetworkInfo | null>(null);
  const [loading, setLoading] = useState(false);
  const suiClient = useSuiClient();

  const checkNetworkInfo = async () => {
    console.log('Checking network info...');
    setLoading(true);
    try {
      const config = networkConfigService.getNetworkConfig();
      console.log('Network config:', config);

      // Get current epoch from the network
      const checkpoints = await suiClient.getCheckpoints({
        limit: 1,
        descendingOrder: true,
      });
      const latestCheckpoint = checkpoints.data[0];
      const currentEpoch = latestCheckpoint?.epoch;

      // Check if contracts are properly configured
      const contractsConfigured =
        config.contracts.hopfunPackageId !== '0x0' &&
        config.contracts.memeConfigId !== '0x0';

      setNetworkInfo({
        appNetwork: config.network,
        appRpcUrl: config.rpcUrl,
        currentEpoch: currentEpoch || 'Unknown',
        contractsConfigured,
      });
    } catch (error) {
      setNetworkInfo({
        appNetwork: 'Unknown',
        appRpcUrl: 'Unknown',
        contractsConfigured: false,
        error: error instanceof Error ? error.message : String(error),
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    checkNetworkInfo();
  }, []);

  if (!networkInfo) {
    return null;
  }

  return (
    <Card className="w-full max-w-md">
      <CardHeader>
        <CardTitle className="text-sm">Network Diagnostic</CardTitle>
      </CardHeader>
      <CardContent className="space-y-2 text-xs">
        <div>
          <strong>App Network:</strong> {networkInfo.appNetwork}
        </div>
        <div>
          <strong>RPC URL:</strong> {networkInfo.appRpcUrl}
        </div>
        <div>
          <strong>Current Epoch:</strong> {networkInfo.currentEpoch}
        </div>
        <div>
          <strong>Max ZKLogin Epoch:</strong>{' '}
          {networkInfo.currentEpoch && networkInfo.currentEpoch !== 'Unknown'
            ? Number(networkInfo.currentEpoch) + 47
            : 'Unknown'}
        </div>
        <div>
          <strong>Contracts Configured:</strong>{' '}
          <span
            className={
              networkInfo.contractsConfigured
                ? 'text-green-600'
                : 'text-red-600'
            }
          >
            {networkInfo.contractsConfigured ? 'Yes' : 'No'}
          </span>
        </div>
        {networkInfo.error && (
          <div className="text-red-600">
            <strong>Error:</strong> {networkInfo.error}
          </div>
        )}
        <Button
          className="mt-2 w-full"
          disabled={loading}
          size="sm"
          onClick={checkNetworkInfo}
        >
          {loading ? 'Checking...' : 'Refresh'}
        </Button>
      </CardContent>
    </Card>
  );
}
