import { formatNumber } from '@/utils/format';

export function formatMarketCap(value: number): string {
  const format = (num: number, suffix: string) => {
    const str = (Math.round(num * 10) / 10).toString();
    return str.endsWith('.0')
      ? `$${str.slice(0, -2)}${suffix}`
      : `$${str}${suffix}`;
  };

  if (value >= 1_000_000_000) return format(value / 1_000_000_000, 'B');
  if (value >= 1_000_000) return format(value / 1_000_000, 'M');
  if (value >= 1_000) return format(value / 1_000, 'K');
  return `$${formatNumber(value, 2)}`;
}
