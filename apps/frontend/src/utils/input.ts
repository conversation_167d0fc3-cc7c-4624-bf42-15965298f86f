import type { ControllerRenderProps, FieldValues, Path } from 'react-hook-form';

/**
 * Formats a number input by removing leading zeros.
 * This function is useful for ensuring that numeric inputs do not start with unnecessary zeros,
 * which can be confusing for users and may lead to validation issues.
 * * @example
 * // Input: "000123"
 * // Output: "123"
 * // Input: "0123.45"
 * // Output: "123.45"
 * // Input: "0.00123"
 * // Output: "0.00123"
 *
 * @param value The input value to format.
 * @returns
 */
export function formatNumberInput(value: string): string {
  // Only remove leading zeros if it's NOT a decimal number
  if (value !== '' && value !== '0' && !value.startsWith('0.')) {
    value = value.replace(/^0+/, '');
    // If the result is empty (user typed only zeros), set to '0'
    if (value === '') value = '0';
  }
  return value;
}

/**
 * Creates a change event handler for a number input field.
 * This handler formats the input value to remove leading zeros
 * and updates the form state accordingly.
 *
 * @param field The field from react-hook-form that needs to handle number input.
 * @returns A change event handler for the input field.
 */
export function createNumberInputHandler<
  TFieldValues extends FieldValues = FieldValues,
  TName extends Path<TFieldValues> = Path<TFieldValues>,
>(field: ControllerRenderProps<TFieldValues, TName>) {
  return (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    const formattedValue = formatNumberInput(value);

    // Update both the form value and the input display
    const numValue = formattedValue === '' ? 0 : Number(formattedValue);
    field.onChange(numValue);

    // Force the input to display the cleaned value
    e.target.value = formattedValue;
  };
}
