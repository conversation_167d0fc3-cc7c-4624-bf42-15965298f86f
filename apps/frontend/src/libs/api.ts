import type { AxiosProgressEvent } from 'axios';
import axios from 'axios';

interface ImgBbResponse {
  data: {
    url: string;
  };
}

const fileToBase64 = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => {
      const base64 = reader.result as string;
      // Remove the data:image/jpeg;base64, prefix
      resolve(base64.split(',')[1]);
    };
    reader.onerror = () =>
      reject(reader.error ?? new Error('Failed to read file.'));
  });
};

export const uploadImageToImgBb = async (
  imageFile: File,
  onUploadProgress: (progressEvent: AxiosProgressEvent) => void,
) => {
  const base64Image = await fileToBase64(imageFile);

  const formData = new FormData();
  formData.append('image', base64Image);

  const response = await axios.post<ImgBbResponse>(
    `https://api.imgbb.com/1/upload?key=${process.env.NEXT_PUBLIC_IMGBB_API_KEY}`,
    formData,
    {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress,
    },
  );

  return response;
};
