import { useMemo } from 'react';

// eslint-disable-next-line import/no-restricted-paths
import { TOKEN_MOCK_DATA } from '@/features/home/<USER>/home-mock-data';

export const useGetTokenDetail = (address: string) => {
  return useMemo(() => {
    const token = TOKEN_MOCK_DATA.find((token) => token.id === address);
    return token ?? TOKEN_MOCK_DATA[0]; // Return first token as fallback
  }, [address]);
};
