@layer base {
  /* Theme colors */
  @import './colors/light.css';
  @import './colors/dark.css';

  html {
    color: var(--foreground);
    background: var(--background);
    font-size: 1rem;
    -ms-text-size-adjust: 100%;
    -webkit-text-size-adjust: 100%;
    -moz-osx-font-smoothing: grayscale;
    -webkit-font-smoothing: antialiased;
  }

  *,
  *:before,
  *:after {
    box-sizing: border-box;
  }

  html,
  body,
  #__next {
    height: calc(
      100vh - env(safe-area-inset-top) - env(safe-area-inset-bottom)
    );
  }

  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}
