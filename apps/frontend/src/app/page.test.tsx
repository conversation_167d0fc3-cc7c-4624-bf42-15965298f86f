import type { RenderResult } from '@testing-library/react';
import { render, screen } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';

import HomePage from './page';

// Mock the section components to avoid SVG and complex component issues
vi.mock('@/features/home/<USER>/hero-section', () => ({
  HeroSection: () => <div data-testid="hero-section">Hero Section</div>,
}));

vi.mock('@/features/home/<USER>/filter-section', () => ({
  FilterSection: () => <div data-testid="filter-section">Filter Section</div>,
}));

vi.mock('@/features/home/<USER>/featured-coins-section', () => ({
  FeaturedCoinsSection: () => (
    <div data-testid="featured-coins-section">Featured Coins Section</div>
  ),
}));

let renderResult: RenderResult;

describe('HomePage', () => {
  beforeEach(() => {
    renderResult = render(<HomePage />);
  });

  it('renders without crashing', () => {
    expect(renderResult).toBeTruthy();
  });

  it('renders the hero section', () => {
    const heroSection = screen.getByTestId('hero-section');
    expect(heroSection).toBeInTheDocument();
    expect(heroSection).toHaveTextContent('Hero Section');
  });

  it('renders the filter section', () => {
    const filterSection = screen.getByTestId('filter-section');
    expect(filterSection).toBeInTheDocument();
    expect(filterSection).toHaveTextContent('Filter Section');
  });

  it('renders the featured coins section', () => {
    const featuredCoinsSection = screen.getByTestId('featured-coins-section');
    expect(featuredCoinsSection).toBeInTheDocument();
    expect(featuredCoinsSection).toHaveTextContent('Featured Coins Section');
  });

  it('renders sections in the correct order', () => {
    const sections = screen.getAllByTestId(/section$/);
    expect(sections).toHaveLength(3);
    expect(sections[0]).toHaveAttribute('data-testid', 'hero-section');
    expect(sections[1]).toHaveAttribute('data-testid', 'filter-section');
    expect(sections[2]).toHaveAttribute(
      'data-testid',
      'featured-coins-section',
    );
  });
});
