{"name": "indexer", "type": "module", "main": "dist/server.js", "types": "dist/server.d.ts", "scripts": {"dev": "tsx watch src/server.ts", "build": "tsup", "start": "node dist/server.js", "lint": "eslint . --ext .ts --fix", "format": "prettier --write .", "format:check": "prettier --check .", "type-check": "tsc --noEmit"}, "dependencies": {"@hopfun/database": "workspace:*", "@hopfun/logger": "workspace:*", "@mysten/sui": "catalog:", "dotenv": "catalog:", "express": "catalog:", "mongodb": "catalog:", "p-queue": "catalog:", "p-retry": "catalog:", "prom-client": "catalog:", "ulid": "catalog:", "zod": "catalog:"}, "devDependencies": {"@hopfun/eslint": "workspace:*", "@hopfun/tsconfig": "workspace:*", "@types/express": "catalog:", "@types/node": "catalog:", "eslint": "catalog:", "prettier": "catalog:", "tsc-alias": "catalog:", "tsup": "catalog:", "tsx": "catalog:", "typescript": "catalog:", "typescript-transform-paths": "catalog:"}}