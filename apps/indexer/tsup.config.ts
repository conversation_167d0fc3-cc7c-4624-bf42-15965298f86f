import { defineConfig } from 'tsup';

export default defineConfig({
  entry: ['src/server.ts'], // Update this to your entry file(s)
  format: ['esm'], // You can add other formats like 'cjs' if needed
  dts: true, // Generate .d.ts files
  outDir: 'dist', // Output directory
  splitting: false, // Disable code splitting if you want a single output file per entry
  sourcemap: true, // Generate sourcemaps if needed
  clean: true, // Clean the output directory before each build
  minify: true, // Disable minification if not needed
  target: 'esnext', // Adjust target as needed
});
