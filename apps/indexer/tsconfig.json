{
  "extends": "@hopfun/tsconfig/base.json",
  "compilerOptions": {
    "outDir": "./dist",
    "baseUrl": "./src",
    "paths": {
      "@/*": ["./*"]
    },
    "plugins": [
      /* Transform paths in output .js files */
      {
        "transform": "typescript-transform-paths"
      },
      /* Transform paths in output .d.ts files */
      {
        "transform": "typescript-transform-paths",
        "afterDeclarations": true
      }
    ]
  },
  "tsc-alias": {
    "resolveFullPaths": true,
    "verbose": false
  },
  "include": ["src/**/*.ts", "src/**/*.d.ts", "tsup.config.ts"],
  "exclude": ["node_modules"]
}
