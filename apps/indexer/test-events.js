import { SuiClient } from '@mysten/sui/client';

async function testEventQuery() {
  const client = new SuiClient({
    url: 'https://fullnode.devnet.sui.io',
  });

  const packageId =
    '0x497ff2769b28664d052601132542a2a77e75339945dc34a494e40fcd680065ac';

  console.log('Testing event queries for package:', packageId);

  try {
    // Test 1: Query all events from the package
    console.log('\n1. Querying all events from package...');
    const allEvents = await client.queryEvents({
      query: {
        MoveModule: {
          package: packageId,
          module: 'events',
        },
      },
      limit: 10,
    });
    console.log('All events found:', allEvents.data.length);
    allEvents.data.forEach((event, i) => {
      console.log(`Event ${i + 1}:`, {
        type: event.type,
        sender: event.sender,
        timestampMs: event.timestampMs,
      });
    });

    // Test 2: Query ConnectorCreated events specifically
    console.log('\n2. Querying ConnectorCreated events...');
    const connectorEvents = await client.queryEvents({
      query: {
        MoveEventType: `${packageId}::events::ConnectorCreated`,
      },
      limit: 10,
    });
    console.log('ConnectorCreated events found:', connectorEvents.data.length);
    connectorEvents.data.forEach((event, i) => {
      console.log(`ConnectorCreated ${i + 1}:`, {
        type: event.type,
        parsedJson: event.parsedJson,
        timestampMs: event.timestampMs,
      });
    });

    // Test 3b: Try to find BondingCurveCreated events in recent events by filtering
    console.log('\n3b. Filtering recent events for BondingCurveCreated...');
    const allRecentEvents = await client.queryEvents({
      query: { All: [] },
      limit: 50,
    });
    const bondingCurveEvents = allRecentEvents.data.filter((event) =>
      event.type.includes('::events::BondingCurveCreated'),
    );
    console.log(
      'BondingCurveCreated events found in recent events:',
      bondingCurveEvents.length,
    );
    bondingCurveEvents.forEach((event, i) => {
      console.log(`BondingCurveCreated ${i + 1}:`, {
        type: event.type,
        parsedJson: event.parsedJson,
        timestampMs: event.timestampMs,
      });
    });

    // Test 3: Query BondingCurveCreated events specifically
    console.log('\n3. Querying BondingCurveCreated events...');
    const curveEvents = await client.queryEvents({
      query: {
        MoveEventType: `${packageId}::events::BondingCurveCreated`,
      },
      limit: 10,
    });
    console.log('BondingCurveCreated events found:', curveEvents.data.length);
    curveEvents.data.forEach((event, i) => {
      console.log(`BondingCurveCreated ${i + 1}:`, {
        type: event.type,
        parsedJson: event.parsedJson,
        timestampMs: event.timestampMs,
      });
    });

    // Test 4: Query recent events from any package to see if there are any events at all
    console.log('\n4. Querying recent events from any package...');
    const recentEvents = await client.queryEvents({
      query: { All: [] },
      limit: 5,
    });
    console.log('Recent events found:', recentEvents.data.length);
    recentEvents.data.forEach((event, i) => {
      console.log(`Recent event ${i + 1}:`, {
        packageId: event.packageId,
        type: event.type,
        timestampMs: event.timestampMs,
      });
    });
  } catch (error) {
    console.error('Error querying events:', error);
  }
}

testEventQuery();
