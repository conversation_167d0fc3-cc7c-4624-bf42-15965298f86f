import { MongoClient } from 'mongodb';

async function testConnection() {
  const uri =
    '****************************************************************';
  const client = new MongoClient(uri);

  try {
    console.log('Attempting to connect to MongoDB...');
    await client.connect();
    console.log('✅ Successfully connected to MongoDB');

    // Test basic operation
    const db = client.db('hopfun');
    const result = await db.admin().ping();
    console.log('✅ Ping successful:', result);

    // List collections
    const collections = await db.listCollections().toArray();
    console.log(
      '📋 Collections:',
      collections.map((c) => c.name),
    );

    // Check connector events
    const connectorEvents = await db
      .collection('connector_created_events')
      .find({})
      .limit(5)
      .toArray();
    console.log('🔗 Connector Events:', connectorEvents.length);
    if (connectorEvents.length > 0) {
      console.log(
        '📄 Sample Connector Event:',
        JSON.stringify(connectorEvents[0], null, 2),
      );
    }

    // Check if tokens collection exists and has data
    try {
      const tokens = await db.collection('tokens').find({}).limit(5).toArray();
      console.log('🪙 Tokens:', tokens.length);
      if (tokens.length > 0) {
        console.log('📄 Sample Token:', JSON.stringify(tokens[0], null, 2));
      }
    } catch (error) {
      console.log('⚠️ No tokens collection yet');
    }

    // Check for bonding curve created events
    try {
      const bondingCurveEvents = await db
        .collection('bonding_curve_created_events')
        .find({})
        .limit(5)
        .toArray();
      console.log('📈 Bonding Curve Events:', bondingCurveEvents.length);
      if (bondingCurveEvents.length > 0) {
        console.log(
          '📄 Sample Bonding Curve Event:',
          JSON.stringify(bondingCurveEvents[0], null, 2),
        );
      }
    } catch (error) {
      console.log('⚠️ No bonding curve events collection yet');
    }
  } catch (error) {
    console.error('❌ Connection failed:', error.message);
    console.error('Full error:', error);
  } finally {
    await client.close();
  }
}

testConnection();
