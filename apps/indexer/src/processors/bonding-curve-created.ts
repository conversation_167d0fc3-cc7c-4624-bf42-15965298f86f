import { TokenStatus } from '@hopfun/database';
import { logger } from '@hopfun/logger';
import type { SuiEvent } from '@mysten/sui/client';
import { ObjectId } from 'mongodb';

import type { Network } from '@/config/environment';
import { EventValidator } from '@/sui/events';

import type { ProcessingContext, ProcessingResult } from './base';
import { BaseEventProcessor } from './base';

export class BondingCurveCreatedProcessor extends BaseEventProcessor {
  constructor(network: Network) {
    super(network);
  }

  getEventType(): string {
    return 'BondingCurveCreated';
  }

  protected validateEvent(event: SuiEvent): boolean {
    return (
      event.parsedJson != null &&
      typeof event.parsedJson === 'object' &&
      EventValidator.isValidBondingCurveCreatedEvent(
        event.parsedJson as Record<string, unknown>,
      )
    );
  }

  protected async checkEventSpecificProcessing(
    event: SuiEvent,
  ): Promise<boolean> {
    try {
      const existingEvent = await this.db.bondingCurveCreatedEvent.findUnique({
        where: {
          transactionId: event.id.txDigest,
        },
      });

      return !!existingEvent;
    } catch (error) {
      logger.error(
        {
          eventId: event.id,
          error: error instanceof Error ? error.message : String(error),
        },
        'Error checking bonding curve created event processing',
      );
      return false;
    }
  }

  async process(context: ProcessingContext): Promise<ProcessingResult> {
    const { event, network } = context;

    if (
      !event.parsedJson ||
      !EventValidator.isValidBondingCurveCreatedEvent(event.parsedJson)
    ) {
      return {
        success: false,
        error: 'Invalid BondingCurveCreated event data',
        shouldRetry: false,
      };
    }

    const eventData = event.parsedJson;

    try {
      await this.withTransaction(async (tx) => {
        // Store the raw bonding curve created event
        await tx.bondingCurveCreatedEvent.create({
          data: {
            id: new ObjectId().toString(),
            curveId: eventData.curve_id,
            creator: eventData.creator,
            coinName: eventData.coin_name,
            ticker: eventData.ticker,
            description: eventData.description,
            imageUrl: eventData.image_url ?? null,
            twitter: eventData.twitter,
            website: eventData.website,
            telegram: eventData.telegram,
            transactionId: event.id.txDigest,
            createdAt: new Date(Number(event.timestampMs)),
            package: event.packageId,
            module: event.type.split('::')[1] ?? 'events',
            eventType: this.getEventType(),
            network,
          },
        });

        // Create the aggregated Token record
        const token = await tx.token.create({
          data: {
            id: new ObjectId().toString(),
            curveId: eventData.curve_id,
            creator: eventData.creator,
            coinName: eventData.coin_name,
            ticker: eventData.ticker,
            description: eventData.description,
            imageUrl: eventData.image_url ?? null,
            twitter: eventData.twitter,
            website: eventData.website,
            telegram: eventData.telegram,
            totalSupply: eventData.total_supply,
            network,
            status: TokenStatus.ACTIVE,
            createdAt: new Date(Number(event.timestampMs)),
          },
        });

        // Create initial TokenStats record
        await tx.tokenStats.create({
          data: {
            id: new ObjectId().toString(),
            tokenId: token.id,
            curveId: eventData.curve_id,
            network,
          },
        });

        logger.info(
          {
            tokenId: token.id,
            curveId: eventData.curve_id,
            ticker: eventData.ticker,
            creator: eventData.creator,
            network,
          },
          'Created new token and stats',
        );
      });

      // Mark as processed
      await this.markEventProcessed(event);

      return {
        success: true,
        data: {
          curveId: eventData.curve_id,
          ticker: eventData.ticker,
          creator: eventData.creator,
          transactionId: event.id.txDigest,
        },
      };
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);

      // Check if this is a duplicate curve ID error
      if (
        errorMessage.includes('duplicate') ||
        errorMessage.includes('unique')
      ) {
        logger.warn(
          {
            curveId: eventData.curve_id,
            transactionId: event.id.txDigest,
            error: errorMessage,
          },
          'Duplicate bonding curve creation detected',
        );

        // Mark as processed since the data already exists
        await this.markEventProcessed(event);

        return {
          success: true,
          data: {
            curveId: eventData.curve_id,
            ticker: eventData.ticker,
            skipped: true,
            reason: 'duplicate_curve',
          },
        };
      }

      logger.error(
        {
          curveId: eventData.curve_id,
          ticker: eventData.ticker,
          creator: eventData.creator,
          transactionId: event.id.txDigest,
          error: errorMessage,
          network,
        },
        'Failed to process BondingCurveCreated event',
      );

      // Add to processing queue for retry
      await this.addToProcessingQueue(event, errorMessage);

      return {
        success: false,
        error: errorMessage,
        shouldRetry: this.shouldRetryError(error),
      };
    }
  }
}

export default BondingCurveCreatedProcessor;
