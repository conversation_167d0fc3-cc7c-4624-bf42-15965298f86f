import type { <PERSON><PERSON><PERSON>, PrismaClient } from '@hopfun/database';
import { TokenStatus } from '@hopfun/database';
import { logger } from '@hopfun/logger';
import type { SuiEvent } from '@mysten/sui/client';
import { ObjectId } from 'mongodb';

import type { Network } from '@/config/environment';
import { EventValidator } from '@/sui/events';

import type { ProcessingContext, ProcessingResult } from './base';
import { BaseEventProcessor } from './base';

export class BondingCurveMigrateProcessor extends BaseEventProcessor {
  constructor(network: Network) {
    super(network);
  }

  getEventType(): string {
    return 'BondingCurveMigrate';
  }

  protected validateEvent(event: SuiEvent): boolean {
    return (
      event.parsedJson != null &&
      typeof event.parsedJson === 'object' &&
      EventValidator.isValidBondingCurveMigrateEvent(
        event.parsedJson as Record<string, unknown>,
      )
    );
  }

  protected async checkEventSpecificProcessing(
    event: SuiEvent,
  ): Promise<boolean> {
    try {
      const existingEvent = await this.db.bondingCurveMigrateEvent.findUnique({
        where: {
          transactionId: event.id.txDigest,
        },
      });

      return !!existingEvent;
    } catch (error) {
      logger.error(
        {
          eventId: event.id,
          error: error instanceof Error ? error.message : String(error),
        },
        'Error checking bonding curve migrate event processing',
      );
      return false;
    }
  }

  async process(context: ProcessingContext): Promise<ProcessingResult> {
    const { event, network } = context;

    if (
      !event.parsedJson ||
      !EventValidator.isValidBondingCurveMigrateEvent(event.parsedJson)
    ) {
      return {
        success: false,
        error: 'Invalid BondingCurveMigrate event data',
        shouldRetry: false,
      };
    }

    const eventData = event.parsedJson;

    try {
      await this.withTransaction(async (tx) => {
        // Store the raw bonding curve migrate event
        await tx.bondingCurveMigrateEvent.create({
          data: {
            id: new ObjectId().toString(),
            curveId: eventData.curve_id,
            toPoolId: eventData.to_pool_id,
            transactionId: event.id.txDigest,
            createdAt: new Date(Number(event.timestampMs)),
            package: event.packageId,
            module: event.type.split('::')[1] || 'events',
            eventType: this.getEventType(),
            network,
          },
        });

        // Find and update the token status
        const token = await tx.token.findUnique({
          where: { curveId: eventData.curve_id },
          include: { stats: true },
        });

        if (!token) {
          throw new Error(
            `Token not found for curve ID: ${eventData.curve_id}`,
          );
        }

        // Update token status to MIGRATED and set pool ID
        await tx.token.update({
          where: { id: token.id },
          data: {
            status: TokenStatus.MIGRATED,
            poolId: eventData.to_pool_id,
            migratedAt: new Date(Number(event.timestampMs)),
            updatedAt: new Date(Number(event.timestampMs)),
          },
        });

        // Calculate final metrics before migration
        const finalMarketCap = this.calculateMarketCap(
          token.totalSupply,
          token.currentPrice,
        );

        // Update token stats with migration metrics
        await tx.tokenStats.updateMany({
          where: { curveId: eventData.curve_id },
          data: {
            lastUpdated: new Date(Number(event.timestampMs)),
          },
        });

        // Update final holder count and statistics
        await this.updateFinalHolderStats(tx, token.id, eventData.curve_id);

        // Log migration completion
        await this.logMigrationCompletion(
          tx,
          token,
          eventData.to_pool_id,
          finalMarketCap,
          event.id.txDigest,
        );

        logger.info(
          {
            curveId: eventData.curve_id,
            tokenId: token.id,
            ticker: token.ticker,
            toPoolId: eventData.to_pool_id,
            finalMarketCap,
            transactionId: event.id.txDigest,
            network,
          },
          'Processed BondingCurveMigrate event',
        );
      });

      // Mark as processed
      await this.markEventProcessed(event);

      return {
        success: true,
        data: {
          curveId: eventData.curve_id,
          toPoolId: eventData.to_pool_id,
          status: 'MIGRATED',
          transactionId: event.id.txDigest,
        },
      };
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);

      logger.error(
        {
          curveId: eventData.curve_id,
          toPoolId: eventData.to_pool_id,
          transactionId: event.id.txDigest,
          error: errorMessage,
          network,
        },
        'Failed to process BondingCurveMigrate event',
      );

      // Add to processing queue for retry
      await this.addToProcessingQueue(event, errorMessage);

      return {
        success: false,
        error: errorMessage,
        shouldRetry: this.shouldRetryError(error),
      };
    }
  }

  private calculateMarketCap(
    totalSupply: string,
    currentPrice: string,
  ): string {
    try {
      const supply = BigInt(totalSupply);
      const price = BigInt(currentPrice);

      // Market cap = total supply * current price
      // Assuming price is already scaled appropriately
      const marketCap = (supply * price) / BigInt(1000000000); // Adjust for price scalar

      return marketCap.toString();
    } catch (error) {
      logger.warn(
        {
          totalSupply,
          currentPrice,
          error: error instanceof Error ? error.message : String(error),
        },
        'Error calculating market cap',
      );
      return '0';
    }
  }

  private async updateFinalHolderStats(
    tx: Omit<
      PrismaClient,
      '$connect' | '$disconnect' | '$on' | '$transaction' | '$use' | '$extends'
    >,
    tokenId: string,
    curveId: string,
  ): Promise<void> {
    try {
      // Get all current holders with positive balances
      const holders = await tx.userHolding.findMany({
        where: {
          tokenId,
          tokenAmount: {
            gt: '0',
          },
        },
      });

      // Calculate final holder statistics
      const holderCount = holders.length;
      let totalTokensHeld = BigInt(0);

      for (const holder of holders) {
        totalTokensHeld += BigInt(holder.tokenAmount);
      }

      // Update token stats with final holder information
      await tx.tokenStats.updateMany({
        where: { curveId },
        data: {
          totalHolders: holderCount,
        },
      });

      logger.info(
        {
          tokenId,
          curveId,
          totalHolders: holderCount,
          totalTokensHeld: totalTokensHeld.toString(),
        },
        'Updated final holder stats for migration',
      );
    } catch (error) {
      logger.error(
        {
          tokenId,
          curveId,
          error: error instanceof Error ? error.message : String(error),
        },
        'Error updating final holder stats',
      );
    }
  }

  // eslint-disable-next-line max-params
  private async logMigrationCompletion(
    tx: Omit<
      PrismaClient,
      '$connect' | '$disconnect' | '$on' | '$transaction' | '$use' | '$extends'
    >,
    token: Prisma.TokenGetPayload<{
      include: { stats: true };
    }>,
    toPoolId: string,
    finalMarketCap: string,
    transactionId: string,
  ): Promise<void> {
    try {
      // Get final statistics for logging
      const stats = await tx.tokenStats.findUnique({
        where: { curveId: token.curveId },
      });

      const migrationSummary = {
        tokenId: token.id,
        curveId: token.curveId,
        ticker: token.ticker,
        coinName: token.coinName,
        creator: token.creator,
        toPoolId,
        finalMarketCap,
        totalSupply: token.totalSupply,
        finalPrice: token.currentPrice,
        totalVolume: stats?.volumeTotal ?? '0',
        totalTransactions: stats?.transactionsTotal ?? 0,
        totalHolders: stats?.totalHolders ?? 0,
        createdAt: token.createdAt,
        completedAt: token.completedAt,
        migratedAt: token.migratedAt,
        transactionId,
      };

      logger.info(migrationSummary, 'Token migration completed successfully');

      // You could also store this summary in a separate table for analytics
      // await tx.migrationSummary.create({ data: migrationSummary });
    } catch (error) {
      logger.error(
        {
          tokenId: token.id,
          curveId: token.curveId,
          error: error instanceof Error ? error.message : String(error),
        },
        'Error logging migration completion',
      );
    }
  }
}

export default BondingCurveMigrateProcessor;
