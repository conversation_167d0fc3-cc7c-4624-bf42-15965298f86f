import type { PrismaClient } from '@hopfun/database';
import { TransactionType } from '@hopfun/database';
import { logger } from '@hopfun/logger';
import type { SuiEvent } from '@mysten/sui/client';
import { ObjectId } from 'mongodb';

import type { Network } from '@/config/environment';
import { EventValidator } from '@/sui/events';

import type { ProcessingContext, ProcessingResult } from './base';
import { BaseEventProcessor } from './base';

export class BondingCurveBuyProcessor extends BaseEventProcessor {
  constructor(network: Network) {
    super(network);
  }

  getEventType(): string {
    return 'BondingCurveBuy';
  }

  protected validateEvent(event: SuiEvent): boolean {
    return (
      event.parsedJson != null &&
      typeof event.parsedJson === 'object' &&
      EventValidator.isValidBondingCurveBuyEvent(
        event.parsedJson as Record<string, unknown>,
      )
    );
  }

  protected async checkEventSpecificProcessing(
    event: SuiEvent,
  ): Promise<boolean> {
    try {
      const existingTransaction =
        await this.db.bondingCurveTransaction.findUnique({
          where: {
            transactionId: event.id.txDigest,
          },
        });

      return !!existingTransaction;
    } catch (error) {
      logger.error(
        {
          eventId: event.id,
          error: error instanceof Error ? error.message : String(error),
        },
        'Error checking bonding curve buy event processing',
      );
      return false;
    }
  }

  async process(context: ProcessingContext): Promise<ProcessingResult> {
    const { event, network } = context;

    if (
      !event.parsedJson ||
      !EventValidator.isValidBondingCurveBuyEvent(event.parsedJson)
    ) {
      return {
        success: false,
        error: 'Invalid BondingCurveBuy event data',
        shouldRetry: false,
      };
    }

    const eventData = event.parsedJson;

    try {
      await this.db.$transaction(async (tx) => {
        // Find the token by curve ID
        const token = await tx.token.findUnique({
          where: { curveId: eventData.curve_id },
          include: { stats: true },
        });

        if (!token) {
          throw new Error(
            `Token not found for curve ID: ${eventData.curve_id}`,
          );
        }

        // Calculate price impact
        const priceImpact = this.calculatePriceImpact(
          eventData.pre_price,
          eventData.post_price,
        );

        // Store the raw transaction event
        await tx.bondingCurveTransaction.create({
          data: {
            id: new ObjectId().toString(),
            curveId: eventData.curve_id,
            eventType: TransactionType.BUY,
            suiAmount: eventData.sui_amount,
            tokenAmount: eventData.token_amount,
            prePrice: eventData.pre_price,
            postPrice: eventData.post_price,
            sender: eventData.sender,
            isDevBuy: eventData.is_dev_buy,
            virtualSuiAmount: eventData.virtual_sui_amount,
            postSuiBalance: eventData.post_sui_balance,
            postTokenBalance: eventData.post_token_balance,
            availableTokenReserves: eventData.available_token_reserves,
            transactionId: event.id.txDigest,
            createdAt: new Date(Number(event.timestampMs)),
            package: event.packageId,
            module: event.type.split('::')[1] || 'events',
            network,
          },
        });

        // Store the transaction in the aggregated table
        await tx.tokenTransaction.create({
          data: {
            id: new ObjectId().toString(),
            tokenId: token.id,
            curveId: eventData.curve_id,
            transactionId: event.id.txDigest,
            network,
            eventType: TransactionType.BUY,
            suiAmount: eventData.sui_amount,
            tokenAmount: eventData.token_amount,
            prePrice: eventData.pre_price,
            postPrice: eventData.post_price,
            priceImpact,
            sender: eventData.sender,
            isDevBuy: eventData.is_dev_buy,
            virtualSuiAmount: eventData.virtual_sui_amount,
            postSuiBalance: eventData.post_sui_balance,
            postTokenBalance: eventData.post_token_balance,
            availableTokenReserves: eventData.available_token_reserves,
            createdAt: new Date(Number(event.timestampMs)),
            blockHeight: context.blockHeight,
            txDigest: event.id.txDigest,
          },
        });

        // Update token's current state
        await tx.token.update({
          where: { id: token.id },
          data: {
            currentPrice: eventData.post_price,
            virtualSuiAmount: eventData.virtual_sui_amount,
            suiBalance: eventData.post_sui_balance,
            tokenBalance: eventData.post_token_balance,
            availableTokenReserves: eventData.available_token_reserves,
            updatedAt: new Date(Number(event.timestampMs)),
          },
        });

        // Update or create user holding
        await this.updateUserHolding(
          tx,
          token.id,
          eventData.curve_id,
          eventData.sender,
          network,
          eventData.token_amount,
          eventData.sui_amount,
          'BUY',
          new Date(Number(event.timestampMs)),
        );

        // Update token statistics
        await this.updateTokenStats(
          tx,
          token.stats?.id ?? token.id,
          eventData.sui_amount,
          eventData.token_amount,
          'BUY',
          new Date(Number(event.timestampMs)),
        );

        logger.info(
          {
            curveId: eventData.curve_id,
            sender: eventData.sender,
            suiAmount: eventData.sui_amount,
            tokenAmount: eventData.token_amount,
            isDevBuy: eventData.is_dev_buy,
            transactionId: event.id.txDigest,
            network,
          },
          'Processed BondingCurveBuy event',
        );
      });

      // Mark as processed
      await this.markEventProcessed(event);

      return {
        success: true,
        data: {
          curveId: eventData.curve_id,
          sender: eventData.sender,
          transactionType: 'BUY',
          suiAmount: eventData.sui_amount,
          tokenAmount: eventData.token_amount,
          transactionId: event.id.txDigest,
        },
      };
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);

      logger.error(
        {
          curveId: eventData.curve_id,
          sender: eventData.sender,
          transactionId: event.id.txDigest,
          error: errorMessage,
          network,
        },
        'Failed to process BondingCurveBuy event',
      );

      // Add to processing queue for retry
      await this.addToProcessingQueue(event, errorMessage);

      return {
        success: false,
        error: errorMessage,
        shouldRetry: this.shouldRetryError(error),
      };
    }
  }

  // eslint-disable-next-line max-params
  private async updateUserHolding(
    tx: Omit<
      PrismaClient,
      '$connect' | '$disconnect' | '$on' | '$transaction' | '$use' | '$extends'
    >,
    tokenId: string,
    curveId: string,
    userAddress: string,
    network: Network,
    tokenAmount: string,
    suiAmount: string,
    action: 'BUY' | 'SELL',
    timestamp: Date,
  ): Promise<void> {
    const existing = await tx.userHolding.findUnique({
      where: {
        tokenId_userAddress: {
          tokenId,
          userAddress,
        },
      },
    });

    if (existing) {
      // Update existing holding
      const currentTokenAmount = BigInt(existing.tokenAmount);
      const currentInvested = BigInt(existing.totalInvested);
      const transactionAmount = BigInt(tokenAmount);
      const transactionSuiAmount = BigInt(suiAmount);

      let newTokenAmount: bigint;
      let newTotalInvested: bigint;
      let newAvgBuyPrice: string;
      let newBuyCount = existing.buyCount;
      let newSellCount = existing.sellCount;

      if (action === 'BUY') {
        newTokenAmount = currentTokenAmount + transactionAmount;
        newTotalInvested = currentInvested + transactionSuiAmount;
        newBuyCount += 1;

        // Calculate new average buy price
        newAvgBuyPrice =
          newTokenAmount > BigInt(0)
            ? (
                (newTotalInvested * BigInt(1000000000)) /
                newTokenAmount
              ).toString()
            : '0';
      } else {
        // SELL logic would go here
        newTokenAmount = currentTokenAmount;
        newTotalInvested = currentInvested;
        newSellCount += 1;
        newAvgBuyPrice = existing.avgBuyPrice;
      }

      await tx.userHolding.update({
        where: {
          tokenId_userAddress: {
            tokenId,
            userAddress,
          },
        },
        data: {
          tokenAmount: newTokenAmount.toString(),
          totalInvested: newTotalInvested.toString(),
          avgBuyPrice: newAvgBuyPrice,
          buyCount: newBuyCount,
          sellCount: newSellCount,
          lastActivity: timestamp,
        },
      });
    } else {
      // Create new holding
      const avgBuyPrice =
        BigInt(tokenAmount) > BigInt(0)
          ? (
              (BigInt(suiAmount) * BigInt(1000000000)) /
              BigInt(tokenAmount)
            ).toString()
          : '0';

      await tx.userHolding.create({
        data: {
          id: new ObjectId().toString(),
          tokenId,
          curveId,
          userAddress,
          network,
          tokenAmount,
          avgBuyPrice,
          totalInvested: suiAmount,
          buyCount: action === 'BUY' ? 1 : 0,
          sellCount: action === 'SELL' ? 1 : 0,
          firstBuy: action === 'BUY' ? timestamp : null,
          lastActivity: timestamp,
        },
      });
    }
  }

  // eslint-disable-next-line max-params
  private async updateTokenStats(
    tx: Omit<
      PrismaClient,
      '$connect' | '$disconnect' | '$on' | '$transaction' | '$use' | '$extends'
    >,
    tokenStatsId: string,
    suiAmount: string,
    tokenAmount: string,
    action: 'BUY' | 'SELL',
    timestamp: Date,
  ): Promise<void> {
    const now = new Date();
    const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);

    const existing = await tx.tokenStats.findUnique({
      where: { id: tokenStatsId },
    });

    if (!existing) {
      logger.warn({ tokenStatsId }, 'TokenStats not found for update');
      return;
    }

    const updateData: Partial<typeof existing> = {
      volumeTotal: (
        BigInt(existing.volumeTotal) + BigInt(suiAmount)
      ).toString(),
      transactionsTotal: existing.transactionsTotal + 1,
      lastUpdated: now,
    };

    // Update 24h stats if transaction is within last 24 hours
    if (timestamp >= oneDayAgo) {
      updateData.volume24h = (
        BigInt(existing.volume24h) + BigInt(suiAmount)
      ).toString();
      updateData.transactions24h = existing.transactions24h + 1;

      if (action === 'BUY') {
        updateData.buyVolume24h = (
          BigInt(existing.buyVolume24h) + BigInt(suiAmount)
        ).toString();
        updateData.buyCount24h = existing.buyCount24h + 1;
      } else {
        updateData.sellVolume24h = (
          BigInt(existing.sellVolume24h) + BigInt(suiAmount)
        ).toString();
        updateData.sellCount24h = existing.sellCount24h + 1;
      }
    }

    await tx.tokenStats.update({
      where: { id: tokenStatsId },
      data: updateData,
    });
  }
}

export default BondingCurveBuyProcessor;
