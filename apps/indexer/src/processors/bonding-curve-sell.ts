import type { PrismaClient } from '@hopfun/database';
import { TransactionType } from '@hopfun/database';
import { logger } from '@hopfun/logger';
import type { SuiEvent } from '@mysten/sui/client';
import { ObjectId } from 'mongodb';

import type { Network } from '@/config/environment';
import { EventValidator } from '@/sui/events';

import type { ProcessingContext, ProcessingResult } from './base';
import { BaseEventProcessor } from './base';

export class BondingCurveSellProcessor extends BaseEventProcessor {
  constructor(network: Network) {
    super(network);
  }

  getEventType(): string {
    return 'BondingCurveSell';
  }

  protected validateEvent(event: SuiEvent): boolean {
    return (
      event.parsedJson != null &&
      typeof event.parsedJson === 'object' &&
      EventValidator.isValidBondingCurveSellEvent(
        event.parsedJson as Record<string, unknown>,
      )
    );
  }

  protected async checkEventSpecificProcessing(
    event: SuiEvent,
  ): Promise<boolean> {
    try {
      const existingTransaction =
        await this.db.bondingCurveTransaction.findUnique({
          where: {
            transactionId: event.id.txDigest,
          },
        });

      return !!existingTransaction;
    } catch (error) {
      logger.error(
        {
          eventId: event.id,
          error: error instanceof Error ? error.message : String(error),
        },
        'Error checking bonding curve sell event processing',
      );
      return false;
    }
  }

  async process(context: ProcessingContext): Promise<ProcessingResult> {
    const { event, network } = context;

    if (
      !event.parsedJson ||
      !EventValidator.isValidBondingCurveSellEvent(event.parsedJson)
    ) {
      return {
        success: false,
        error: 'Invalid BondingCurveSell event data',
        shouldRetry: false,
      };
    }

    const eventData = event.parsedJson;

    try {
      await this.withTransaction(async (tx) => {
        // Find the token by curve ID
        const token = await tx.token.findUnique({
          where: { curveId: eventData.curve_id },
          include: { stats: true },
        });

        if (!token) {
          throw new Error(
            `Token not found for curve ID: ${eventData.curve_id}`,
          );
        }

        // Get sender from transaction context (sell events don't include sender in event data)
        // We'll need to extract this from the transaction details
        const sender = context.event.sender || 'unknown';

        // Calculate price impact
        const priceImpact = this.calculatePriceImpact(
          eventData.pre_price,
          eventData.post_price,
        );

        // Store the raw transaction event
        await tx.bondingCurveTransaction.create({
          data: {
            id: new ObjectId().toString(),
            curveId: eventData.curve_id,
            eventType: TransactionType.SELL,
            suiAmount: eventData.sui_amount,
            tokenAmount: eventData.token_amount,
            prePrice: eventData.pre_price,
            postPrice: eventData.post_price,
            sender,
            virtualSuiAmount: eventData.virtual_sui_amount,
            postSuiBalance: eventData.post_sui_balance,
            postTokenBalance: eventData.post_token_balance,
            availableTokenReserves: eventData.available_token_reserves,
            transactionId: event.id.txDigest,
            createdAt: new Date(Number(event.timestampMs)),
            package: event.packageId,
            module: event.type.split('::')[1] || 'events',
            network,
          },
        });

        // Store the transaction in the aggregated table
        await tx.tokenTransaction.create({
          data: {
            id: new ObjectId().toString(),
            tokenId: token.id,
            curveId: eventData.curve_id,
            transactionId: event.id.txDigest,
            network,
            eventType: TransactionType.SELL,
            suiAmount: eventData.sui_amount,
            tokenAmount: eventData.token_amount,
            prePrice: eventData.pre_price,
            postPrice: eventData.post_price,
            priceImpact,
            sender,
            isDevBuy: false,
            virtualSuiAmount: eventData.virtual_sui_amount,
            postSuiBalance: eventData.post_sui_balance,
            postTokenBalance: eventData.post_token_balance,
            availableTokenReserves: eventData.available_token_reserves,
            createdAt: new Date(Number(event.timestampMs)),
            blockHeight: context.blockHeight,
            txDigest: event.id.txDigest,
          },
        });

        // Update token's current state
        await tx.token.update({
          where: { id: token.id },
          data: {
            currentPrice: eventData.post_price,
            virtualSuiAmount: eventData.virtual_sui_amount,
            suiBalance: eventData.post_sui_balance,
            tokenBalance: eventData.post_token_balance,
            availableTokenReserves: eventData.available_token_reserves,
            updatedAt: new Date(Number(event.timestampMs)),
          },
        });

        // Update user holding
        await this.updateUserHolding(
          tx,
          token.id,
          eventData.curve_id,
          sender,
          network,
          eventData.token_amount,
          eventData.sui_amount,
          'SELL',
          new Date(Number(event.timestampMs)),
        );

        // Update token statistics
        await this.updateTokenStats(
          tx,
          token.stats?.id ?? token.id,
          eventData.sui_amount,
          eventData.token_amount,
          'SELL',
          new Date(Number(event.timestampMs)),
        );

        logger.info(
          {
            curveId: eventData.curve_id,
            sender,
            suiAmount: eventData.sui_amount,
            tokenAmount: eventData.token_amount,
            transactionId: event.id.txDigest,
            network,
          },
          'Processed BondingCurveSell event',
        );
      });

      // Mark as processed
      await this.markEventProcessed(event);

      return {
        success: true,
        data: {
          curveId: eventData.curve_id,
          transactionType: 'SELL',
          suiAmount: eventData.sui_amount,
          tokenAmount: eventData.token_amount,
          transactionId: event.id.txDigest,
        },
      };
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);

      logger.error(
        {
          curveId: eventData.curve_id,
          transactionId: event.id.txDigest,
          error: errorMessage,
          network,
        },
        'Failed to process BondingCurveSell event',
      );

      // Add to processing queue for retry
      await this.addToProcessingQueue(event, errorMessage);

      return {
        success: false,
        error: errorMessage,
        shouldRetry: this.shouldRetryError(error),
      };
    }
  }

  // eslint-disable-next-line max-params
  private async updateUserHolding(
    tx: Omit<
      PrismaClient,
      '$connect' | '$disconnect' | '$on' | '$transaction' | '$use' | '$extends'
    >,
    tokenId: string,
    curveId: string,
    userAddress: string,
    network: Network,
    tokenAmount: string,
    suiAmount: string,
    action: 'SELL',
    timestamp: Date,
  ): Promise<void> {
    const existing = await tx.userHolding.findUnique({
      where: {
        tokenId_userAddress: {
          tokenId,
          userAddress,
        },
      },
    });

    if (existing) {
      // Update existing holding for sell
      const currentTokenAmount = BigInt(existing.tokenAmount);
      const transactionTokenAmount = BigInt(tokenAmount);
      const transactionSuiAmount = BigInt(suiAmount);
      const currentRealized = BigInt(existing.realizedPnl);

      // Calculate new token amount (subtract sold tokens)
      const newTokenAmount = currentTokenAmount - transactionTokenAmount;

      // Calculate realized PnL for this sale
      const avgBuyPrice = BigInt(existing.avgBuyPrice);
      const salePrice =
        transactionTokenAmount > BigInt(0)
          ? (transactionSuiAmount * BigInt(1000000000)) / transactionTokenAmount
          : BigInt(0);

      const realizePnlFromSale =
        (transactionTokenAmount * (salePrice - avgBuyPrice)) /
        BigInt(1000000000);
      const newRealizedPnl = currentRealized + realizePnlFromSale;

      await tx.userHolding.update({
        where: {
          tokenId_userAddress: {
            tokenId,
            userAddress,
          },
        },
        data: {
          tokenAmount: newTokenAmount.toString(),
          realizedPnl: newRealizedPnl.toString(),
          sellCount: existing.sellCount + 1,
          lastActivity: timestamp,
        },
      });
    } else {
      // This shouldn't happen - selling without buying first
      logger.warn(
        {
          tokenId,
          userAddress,
          tokenAmount,
          suiAmount,
        },
        'User selling tokens without existing holding',
      );

      // Create a negative holding to track the sell
      await tx.userHolding.create({
        data: {
          id: new ObjectId().toString(),
          tokenId,
          curveId,
          userAddress,
          network,
          tokenAmount: (-BigInt(tokenAmount)).toString(),
          avgBuyPrice: '0',
          totalInvested: '0',
          realizedPnl: suiAmount, // Full amount as realized gain
          buyCount: 0,
          sellCount: 1,
          lastActivity: timestamp,
        },
      });
    }
  }

  // eslint-disable-next-line max-params
  private async updateTokenStats(
    tx: Omit<
      PrismaClient,
      '$connect' | '$disconnect' | '$on' | '$transaction' | '$use' | '$extends'
    >,
    tokenStatsId: string,
    suiAmount: string,
    tokenAmount: string,
    action: 'SELL',
    timestamp: Date,
  ): Promise<void> {
    const now = new Date();
    const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);

    const existing = await tx.tokenStats.findUnique({
      where: { id: tokenStatsId },
    });

    if (!existing) {
      logger.warn({ tokenStatsId }, 'TokenStats not found for update');
      return;
    }

    const updateData: Partial<typeof existing> = {
      volumeTotal: (
        BigInt(existing.volumeTotal) + BigInt(suiAmount)
      ).toString(),
      transactionsTotal: existing.transactionsTotal + 1,
      lastUpdated: now,
    };

    // Update 24h stats if transaction is within last 24 hours
    if (timestamp >= oneDayAgo) {
      updateData.volume24h = (
        BigInt(existing.volume24h) + BigInt(suiAmount)
      ).toString();
      updateData.transactions24h = existing.transactions24h + 1;
      updateData.sellVolume24h = (
        BigInt(existing.sellVolume24h) + BigInt(suiAmount)
      ).toString();
      updateData.sellCount24h = existing.sellCount24h + 1;
    }

    await tx.tokenStats.update({
      where: { id: tokenStatsId },
      data: updateData,
    });
  }
}

export default BondingCurveSellProcessor;
