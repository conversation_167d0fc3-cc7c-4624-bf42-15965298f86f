import { SuiClient } from '@mysten/sui/client';

const client = new SuiClient({
  url: 'https://fullnode.devnet.sui.io',
});

const hopfunPackageId = '0x497ff2769b28664d052601132542a2a77e75339945dc34a494e40fcd680065ac';

async function testPackageEvents() {
  console.log('🧪 Testing Package filter...');
  
  try {
    const response = await client.queryEvents({
      query: { Package: hopfunPackageId },
      limit: 20,
      order: 'descending',
    });
    
    console.log('📊 Package events found:', response.data.length);
    
    if (response.data.length > 0) {
      const eventTypes = new Set();
      for (const event of response.data) {
        eventTypes.add(event.type);
      }
      
      console.log('📋 Unique event types found:');
      for (const eventType of eventTypes) {
        console.log(`  - ${eventType}`);
      }
      
      // Look for BondingCurveCreated events specifically
      const bondingCurveEvents = response.data.filter(event => 
        event.type.includes('BondingCurveCreated')
      );
      
      console.log(`\n🎯 BondingCurveCreated events: ${bondingCurveEvents.length}`);
      
      if (bondingCurveEvents.length > 0) {
        console.log('✅ Found BondingCurveCreated events!');
        for (const event of bondingCurveEvents) {
          console.log(`📄 Event: ${event.type}`);
          console.log(`   TX: ${event.id.txDigest}`);
          console.log(`   Time: ${event.timestampMs}`);
        }
      }
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

testPackageEvents();
