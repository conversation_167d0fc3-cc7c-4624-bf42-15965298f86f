import { logger } from '@hopfun/logger';
import type { Context, MiddlewareHandler } from 'hono';
import { endTime, setMetric, startTime } from 'hono/timing';

import { generateUlid } from '@/libs/ulid';

export const requestLogger = (): MiddlewareHandler => {
  return async (c: Context, next) => {
    const start = Date.now();
    const requestId = generateUlid();

    // Get request details
    const method = c.req.method;
    const url = c.req.url;
    const path = c.req.path;
    const query = c.req.query();
    const params = c.req.param();

    // Set a metric for the request ID
    c.res.headers.set('x-request-id', requestId);
    setMetric(c, 'request_id', requestId);
    startTime(c, `${method}:${path}`);

    // Get request body (be careful with this as it consumes the stream)
    let body: unknown = null;
    try {
      if (method !== 'GET' && method !== 'HEAD' && method !== 'DELETE') {
        const contentType = c.req.header('content-type');
        if (contentType?.includes('application/json')) {
          body = await c.req.json();
        } else if (contentType?.includes('application/x-www-form-urlencoded')) {
          body = await c.req.parseBody();
        } else if (contentType?.includes('text/')) {
          body = await c.req.text();
        }
      }
    } catch {
      // If body parsing fails, continue without it
      body = '[Unable to parse body]';
    }

    // Log request details
    logger.info(
      {
        type: 'request',
        method,
        url,
        path,
        headers: c.req.header(),
        query: Object.keys(query).length > 0 ? query : undefined,
        params: Object.keys(params).length > 0 ? params : undefined,
        body: body !== null ? body : undefined,
        userAgent: c.req.header('user-agent'),
        ip: c.req.header('x-forwarded-for') ?? c.req.header('x-real-ip'),
        requestId,
      },
      `${method} ${path}`,
    );

    await next();

    // Capture response body
    let responseBody: unknown = null;
    try {
      const response = c.res.clone();
      const contentType = response.headers.get('content-type');

      if (contentType?.includes('application/json')) {
        responseBody = await response.json();
      } else if (contentType?.includes('text/')) {
        responseBody = await response.text();
      } else {
        responseBody = '[Binary or non-text response]';
      }
    } catch {
      responseBody = '[Unable to parse response]';
    }

    // Log response details
    const duration = new Date().getTime() - start;
    const status = c.res.status;

    // End the timing for the request
    endTime(c, `${method}:${path}`);

    logger.info(
      {
        type: 'response',
        method,
        path,
        status,
        res: responseBody,
        headers: Object.fromEntries(c.res.headers.entries()),
        duration: `${duration}ms`,
        requestId,
      },
      `${method} ${path} - ${status} (${duration}ms)`,
    );
  };
};
