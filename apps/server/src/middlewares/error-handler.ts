import { logger } from '@hopfun/logger';
import type { Context } from 'hono';
import { HTTPException } from 'hono/http-exception';
import { StatusCodes } from 'http-status-codes';
import z, { ZodError } from 'zod';

import { ERROR_CODES } from '@/constants/error-codes';
import { formatResponse } from '@/utils/response';

export const zodErrorHandler = (c: Context, error: ZodError) => {
  const readableError = z.treeifyError(error);
  return formatResponse(c, StatusCodes.BAD_REQUEST, {
    message: 'Invalid request data',
    error: {
      code: ERROR_CODES.INVALID_INPUT,
      details: readableError.errors,
    },
  });
};

/**
 * Handles errors and returns an appropriate response.
 * @param error - The error object.
 * @param c - The context object.
 * @returns The response object.
 */
export const errorHandlers = (error: unknown, c: Context) => {
  // If the error is not an instance of Error, return an internal server error response.
  if (!(error instanceof Error)) {
    return formatResponse(c, StatusCodes.INTERNAL_SERVER_ERROR, {
      message: 'An unexpected error occurred',
    });
  }

  logger.error(`An error occurred: ${error.message}`, { error });

  // If the error is a Zod error, handle it accordingly.
  if (error instanceof ZodError) {
    return zodErrorHandler(c, error);
  }

  // If the error is an instance of HTTPException, return an error response based on its status.
  if (error instanceof HTTPException) {
    return formatResponse(c, error.status, {
      error: {
        details: error.cause,
      },
      message: error.message,
    });
  }

  c.get('sentry').captureException(error);

  // Otherwise, return an internal server error response.
  return formatResponse(c, StatusCodes.INTERNAL_SERVER_ERROR, {
    error: {
      details: error instanceof Error ? error.message : 'Unknown error',
    },
    message: 'An unexpected error occurred',
  });
};
