import type { Context, TypedResponse } from 'hono';
import type { ContentfulStatusCode, StatusCode } from 'hono/utils/http-status';
import { StatusCodes } from 'http-status-codes';

export interface ApiError {
  code?: string;
  details?: unknown;
}

export interface ApiResponse<T> {
  status?: 'success' | 'error';
  message?: string;
  data?: T;
  meta?: Record<string, unknown>;
  error?: ApiError;
}

/**
 * Format response to standard API response
 * @param status - HTTP status code
 * @param response - Response object
 * @returns Formatted response
 */
export function formatResponse<T>(
  // @see https://developer.mozilla.org/en-US/docs/Web/HTTP/Status
  c: Context,
  status: StatusCode,
  { error, message, data, meta }: ApiResponse<T> = {},
): (TypedResponse<ApiResponse<T>> & Response) | Response {
  const response: ApiResponse<T> = {
    status: status >= 400 ? 'error' : 'success',
    message: message ?? getDefaultMessage(status),
    data: data ?? undefined,
    meta: meta ?? undefined,
    error: error ?? undefined,
  };

  // No content response
  if (status === StatusCodes.NO_CONTENT)
    return c.body(null, StatusCodes.NO_CONTENT);

  // Type casing because c.json expects a ContentfulStatusCode as second argument
  return c.json(response, status as ContentfulStatusCode);
}

export interface ErrorResponse<T extends Error> {
  message: string;
  error: T;
  code?: string;
  meta?: Record<string, unknown>;
}

function getDefaultMessage(status: number): string {
  if (status >= 200 && status < 300) {
    return 'Success';
  }

  if (status >= 300 && status < 400) {
    return 'Resource has been moved';
  }

  if (status >= 400) {
    return 'An error occurred, please try again later';
  }

  if (status >= 500) {
    return 'An internal server error occurred, contact support for more information';
  }

  return '';
}
