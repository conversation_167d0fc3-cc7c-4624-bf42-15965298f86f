import type { PrismaClient } from '@hopfun/database';
import { createPrismaClient } from '@hopfun/database';

import { Env } from '@/configs/env';

const db: PrismaClient = createPrismaClient(Env.get('DATABASE_URL'), {
  log: ['query', 'info', 'warn', 'error'],
  errorFormat: Env.isDevelopment ? 'pretty' : 'minimal',
  datasources: {
    db: {
      url: Env.get('DATABASE_URL'),
    },
  },
});

export { db };
