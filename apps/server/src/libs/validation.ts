import type {
  Context,
  Env,
  Input,
  MiddlewareHandler,
  TypedResponse,
  ValidationTargets,
} from 'hono';
import { validator } from 'hono/validator';
import type { ZodError, ZodType } from 'zod';
import { z } from 'zod';

type Hook<T, E extends Env, P extends string, O = object> = (
  result:
    | { success: true; data: T }
    | { success: false; error: ZodError; data: T },
  c: Context<E, P>,
) =>
  | Response
  | Promise<Response>
  | void
  | Promise<Response | void>
  | TypedResponse<O>;

type HasUndefined<T> = undefined extends T ? true : false;

/**
 * Validation class
 */
export class Validation {
  /**
   * Validate the schema with the data
   * @param schema - The schema to validate.
   * @param data - The data to validate.
   * @returns The validated data.
   */
  static validate<T>(schema: ZodType, data: T): T {
    return schema.parse(data) as T;
  }

  /**
   * Custom validator middleware
   * @param target - The target of the validation.
   * @param schema - The schema to validate.
   * @param hook - The hook to run after validation.
   * @returns The middleware handler.
   *
   * @example
   * ```ts
   * const schema = z.object({
   *  name: z.string(),
   *  age: z.number(),
   * });
   *
   * app.post("/author", Validation.validator("json", schema, (c) => {
   *  const body = c.req.valid("json");
   *  return formatResponse(200, { data: body });
   * }));
   * ```
   *
   * NOTE: I copied the code from the below link and pasted it here
   * because I need to modify the error response handler.
   *
   * @see - https://github.com/honojs/middleware/blob/main/packages/zod-validator/src/index.ts
   */
  static validator<
    T extends ZodType,
    Target extends keyof ValidationTargets,
    E extends Env,
    P extends string,
    In = z.input<T>,
    Out = z.output<T>,
    I extends Input = {
      in: HasUndefined<In> extends true
        ? {
            [K in Target]?: K extends 'json'
              ? In
              : HasUndefined<keyof ValidationTargets[K]> extends true
                ? { [K2 in keyof In]?: ValidationTargets[K][K2] }
                : { [K2 in keyof In]: ValidationTargets[K][K2] };
          }
        : {
            [K in Target]: K extends 'json'
              ? In
              : HasUndefined<keyof ValidationTargets[K]> extends true
                ? { [K2 in keyof In]?: ValidationTargets[K][K2] }
                : { [K2 in keyof In]: ValidationTargets[K][K2] };
          };
      out: Record<Target, Out>;
    },
    V extends I = I,
  >(
    target: Target,
    schema: T,
    hook?: Hook<z.infer<T>, E, P>,
  ): MiddlewareHandler<E, P, V> {
    // @ts-expect-error not typed well
    return validator(target, async (value, c) => {
      const result = await schema.safeParseAsync(value);

      if (hook) {
        const hookResult = hook({ data: value, ...result }, c);
        if (hookResult) {
          if (hookResult instanceof Response || hookResult instanceof Promise) {
            return hookResult;
          }
          if ('response' in hookResult) {
            return hookResult.response;
          }
        }
      }

      // Handle validation errors
      if (!result.success) {
        throw new z.ZodError(result.error.issues);
      }

      const data = result.data as z.infer<T>;
      return data;
    });
  }
}
