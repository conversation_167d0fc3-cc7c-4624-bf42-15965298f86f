import { beforeEach, describe, expect, it, vi } from 'vitest';
import type {
  FaucetResult,
  ISuiFaucetService,
} from '../interfaces/wallet.interfaces';
import { RequestFaucetUseCase } from './request-faucet.use-case';

vi.mock('@hopfun/logger', () => ({
  logger: {
    info: vi.fn(),
    error: vi.fn(),
    warn: vi.fn(),
    debug: vi.fn(),
  },
}));

describe('RequestFaucetUseCase', () => {
  let useCase: RequestFaucetUseCase;
  let mockFaucetService: ISuiFaucetService;
  const mockAddress =
    '0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef';

  beforeEach(() => {
    vi.clearAllMocks();
    mockFaucetService = {
      requestFaucet: vi.fn(),
    };
    useCase = new RequestFaucetUseCase(mockFaucetService);
  });

  describe('execute', () => {
    it('should successfully process faucet request', async () => {
      const mockFaucetResult: FaucetResult = {
        transferredGasObjects: [
          {
            id: '0xabc123',
            transferTxDigest: 'digest123',
            amount: 1000000000,
          },
          {
            id: '0xdef456',
            transferTxDigest: 'digest456',
            amount: 2000000000,
          },
        ],
        error: null,
      };

      vi.mocked(mockFaucetService.requestFaucet).mockResolvedValue(
        mockFaucetResult,
      );

      const result = await useCase.execute(mockAddress);

      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data?.address).toBe(mockAddress);
      expect(result.data?.transferredGasObjects).toEqual(
        mockFaucetResult.transferredGasObjects,
      );
      expect(result.data?.totalAmount).toBe(3000000000);
      expect(result.data?.timestamp).toBeDefined();
      expect(result.error).toBeUndefined();

      expect(mockFaucetService.requestFaucet).toHaveBeenCalledWith(mockAddress);
    });

    it('should handle faucet service error response', async () => {
      const mockFaucetResult: FaucetResult = {
        transferredGasObjects: [],
        error: 'Rate limit exceeded',
      };

      vi.mocked(mockFaucetService.requestFaucet).mockResolvedValue(
        mockFaucetResult,
      );

      const result = await useCase.execute(mockAddress);

      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.error?.code).toBe('FAUCET_REQUEST_FAILED');
      expect(result.error?.message).toBe('Rate limit exceeded');
      expect(result.data).toBeUndefined();
    });

    it('should handle service exception with Error object', async () => {
      const error = new Error('Network timeout');
      vi.mocked(mockFaucetService.requestFaucet).mockRejectedValue(error);

      const result = await useCase.execute(mockAddress);

      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.error?.code).toBe('FAUCET_ERROR');
      expect(result.error?.message).toBe('Network timeout');
      expect(result.error?.details).toBeDefined();
      expect(result.data).toBeUndefined();
    });

    it('should handle unknown errors', async () => {
      vi.mocked(mockFaucetService.requestFaucet).mockRejectedValue(
        'Unknown error',
      );

      const result = await useCase.execute(mockAddress);

      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.error?.code).toBe('UNKNOWN_ERROR');
      expect(result.error?.message).toBe(
        'An unknown error occurred while requesting faucet',
      );
      expect(result.data).toBeUndefined();
    });

    it('should calculate total amount correctly for single gas object', async () => {
      const mockFaucetResult: FaucetResult = {
        transferredGasObjects: [
          {
            id: '0xabc123',
            transferTxDigest: 'digest123',
            amount: 5000000000,
          },
        ],
        error: null,
      };

      vi.mocked(mockFaucetService.requestFaucet).mockResolvedValue(
        mockFaucetResult,
      );

      const result = await useCase.execute(mockAddress);

      expect(result.success).toBe(true);
      expect(result.data?.totalAmount).toBe(5000000000);
    });

    it('should handle empty gas objects array', async () => {
      const mockFaucetResult: FaucetResult = {
        transferredGasObjects: [],
        error: null,
      };

      vi.mocked(mockFaucetService.requestFaucet).mockResolvedValue(
        mockFaucetResult,
      );

      const result = await useCase.execute(mockAddress);

      expect(result.success).toBe(true);
      expect(result.data?.transferredGasObjects).toEqual([]);
      expect(result.data?.totalAmount).toBe(0);
    });

    it('should include timestamp in response', async () => {
      const mockFaucetResult: FaucetResult = {
        transferredGasObjects: [
          {
            id: '0xabc123',
            transferTxDigest: 'digest123',
            amount: 1000000000,
          },
        ],
        error: null,
      };

      vi.mocked(mockFaucetService.requestFaucet).mockResolvedValue(
        mockFaucetResult,
      );

      const beforeTime = new Date().toISOString();
      const result = await useCase.execute(mockAddress);
      const afterTime = new Date().toISOString();

      expect(result.success).toBe(true);
      expect(result.data?.timestamp).toBeDefined();
      expect(new Date(result.data!.timestamp).getTime()).toBeGreaterThanOrEqual(
        new Date(beforeTime).getTime(),
      );
      expect(new Date(result.data!.timestamp).getTime()).toBeLessThanOrEqual(
        new Date(afterTime).getTime(),
      );
    });
  });
});
