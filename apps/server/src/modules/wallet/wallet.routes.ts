import { logger } from '@hopfun/logger';
import { Hono } from 'hono';
import { StatusCodes } from 'http-status-codes';

import { formatResponse } from '@/utils/response';

import { GetBalanceRequestSchema } from './dto/get-balance.dto';
import { requestFaucetQuerySchema } from './dto/request-faucet.dto';
import { SuiFaucetService } from './services/sui-faucet.service';
import { GetBalanceUseCase } from './use-cases/get-balance.use-case';
import { RequestFaucetUseCase } from './use-cases/request-faucet.use-case';

const wallet = new Hono()
  .get('/health', (c) => {
    return formatResponse(c, StatusCodes.OK, {
      message: 'Wallet module is healthy',
      data: {
        version: '1.0.0',
        status: 'operational',
      },
    });
  })
  .get('/balance', async (c) => {
    try {
      // Get address from query parameters
      const address = c.req.query('address');

      // Validate request
      const validationResult = GetBalanceRequestSchema.safeParse({ address });

      if (!validationResult.success) {
        logger.warn(
          {
            errors: validationResult.error.flatten(),
            address,
          },
          'Invalid balance request',
        );

        return formatResponse(c, StatusCodes.BAD_REQUEST, {
          error: {
            code: 'VALIDATION_ERROR',
            details: validationResult.error.flatten(),
          },
        });
      }

      // Execute use case
      const getBalanceUseCase = new GetBalanceUseCase();
      const result = await getBalanceUseCase.execute(
        validationResult.data.address,
      );

      if (result.success) {
        return formatResponse(c, StatusCodes.OK, {
          message: 'Balance retrieved successfully',
          data: result.data,
        });
      } else {
        // Determine status code based on error code
        let statusCode = StatusCodes.INTERNAL_SERVER_ERROR;

        if (result.error?.code === 'INVALID_ADDRESS_FORMAT') {
          statusCode = StatusCodes.BAD_REQUEST;
        } else if (result.error?.code === 'ADDRESS_NOT_FOUND') {
          statusCode = StatusCodes.NOT_FOUND;
        } else if (result.error?.code === 'RPC_CONNECTION_ERROR') {
          statusCode = StatusCodes.SERVICE_UNAVAILABLE;
        }

        return formatResponse(c, statusCode, {
          message: result.error?.message,
          error: {
            code: result.error?.code ?? 'UNKNOWN_ERROR',
            details: result.error?.details,
          },
        });
      }
    } catch (error) {
      logger.error(
        {
          error: error instanceof Error ? error.message : String(error),
          path: c.req.path,
          method: c.req.method,
        },
        'Unexpected error in balance endpoint',
      );

      return formatResponse(c, StatusCodes.INTERNAL_SERVER_ERROR, {
        message: 'An unexpected error occurred',
        error: {
          code: 'INTERNAL_SERVER_ERROR',
        },
      });
    }
  })
  .get('/balance/:address', async (c) => {
    try {
      // Get address from path parameters
      const address = c.req.param('address');

      // Validate request
      const validationResult = GetBalanceRequestSchema.safeParse({ address });

      if (!validationResult.success) {
        logger.warn(
          {
            errors: validationResult.error.flatten(),
            address,
          },
          'Invalid balance request',
        );

        return formatResponse(c, StatusCodes.BAD_REQUEST, {
          error: {
            code: 'VALIDATION_ERROR',
            details: validationResult.error.flatten(),
          },
        });
      }

      // Execute use case
      const getBalanceUseCase = new GetBalanceUseCase();
      const result = await getBalanceUseCase.execute(
        validationResult.data.address,
      );

      if (result.success) {
        return formatResponse(c, StatusCodes.OK, {
          message: 'Balance retrieved successfully',
          data: result.data,
        });
      } else {
        // Determine status code based on error code
        let statusCode = StatusCodes.INTERNAL_SERVER_ERROR;

        if (result.error?.code === 'INVALID_ADDRESS_FORMAT') {
          statusCode = StatusCodes.BAD_REQUEST;
        } else if (result.error?.code === 'ADDRESS_NOT_FOUND') {
          statusCode = StatusCodes.NOT_FOUND;
        } else if (result.error?.code === 'RPC_CONNECTION_ERROR') {
          statusCode = StatusCodes.SERVICE_UNAVAILABLE;
        }

        return formatResponse(c, statusCode, {
          message: result.error?.message,
          error: {
            code: result.error?.code ?? 'UNKNOWN_ERROR',
            details: result.error?.details,
          },
        });
      }
    } catch (error) {
      logger.error(
        {
          error: error instanceof Error ? error.message : String(error),
          path: c.req.path,
          method: c.req.method,
        },
        'Unexpected error in balance endpoint',
      );

      return formatResponse(c, StatusCodes.INTERNAL_SERVER_ERROR, {
        message: 'An unexpected error occurred',
        error: {
          code: 'INTERNAL_SERVER_ERROR',
        },
      });
    }
  })
  .get('/faucet', async (c) => {
    try {
      // Get address from query parameters
      const address = c.req.query('address');

      // Validate request
      const validationResult = requestFaucetQuerySchema.safeParse({ address });

      if (!validationResult.success) {
        logger.warn(
          {
            errors: validationResult.error.flatten(),
            address,
          },
          'Invalid faucet request',
        );

        return formatResponse(c, StatusCodes.BAD_REQUEST, {
          error: {
            code: 'VALIDATION_ERROR',
            details: validationResult.error.flatten(),
          },
        });
      }

      // Get network from environment
      const network = process.env.NETWORK ?? 'TESTNET';

      // Execute use case
      const suiFaucetService = new SuiFaucetService(network);
      const requestFaucetUseCase = new RequestFaucetUseCase(suiFaucetService);
      const result = await requestFaucetUseCase.execute(
        validationResult.data.address,
      );

      if (result.success) {
        return formatResponse(c, StatusCodes.OK, {
          message: 'Faucet tokens received successfully',
          data: result.data,
        });
      } else {
        // Determine status code based on error code
        let statusCode = StatusCodes.INTERNAL_SERVER_ERROR;

        if (result.error?.code === 'VALIDATION_ERROR') {
          statusCode = StatusCodes.BAD_REQUEST;
        } else if (
          result.error?.message.includes('limit exceeded') ||
          result.error?.message.includes('rate limit')
        ) {
          statusCode = StatusCodes.TOO_MANY_REQUESTS;
        } else if (result.error?.message.includes('not available')) {
          statusCode = StatusCodes.SERVICE_UNAVAILABLE;
        }

        return formatResponse(c, statusCode, {
          message: result.error?.message,
          error: {
            code: result.error?.code ?? 'FAUCET_ERROR',
            details: result.error?.details,
          },
        });
      }
    } catch (error) {
      logger.error(
        {
          error: error instanceof Error ? error.message : String(error),
          path: c.req.path,
          method: c.req.method,
        },
        'Unexpected error in faucet endpoint',
      );

      return formatResponse(c, StatusCodes.INTERNAL_SERVER_ERROR, {
        message: 'An unexpected error occurred',
        error: {
          code: 'INTERNAL_SERVER_ERROR',
        },
      });
    }
  });

export { wallet };
