import { logger } from '@hopfun/logger';

import type {
  VerifyTokenExistenceResponseDto,
  VerifyTokenResponseDto,
} from '../dto/verify-token.dto';
import type {
  ITokenService,
  IVerifyTokenUseCase,
  VerifyTokenParams,
} from '../interfaces/token.interfaces';
import { TokenService } from '../services/token.service';

export class VerifyTokenUseCase implements IVerifyTokenUseCase {
  private tokenService: ITokenService;

  constructor(tokenService?: ITokenService) {
    this.tokenService = tokenService ?? new TokenService();
  }

  async verifyToken(
    params: VerifyTokenParams,
  ): Promise<VerifyTokenResponseDto> {
    const startTime = Date.now();

    logger.info(params, 'Starting token verification');

    try {
      // Validate input
      this.validateVerifyTokenParams(params);

      // Verify token
      const result = await this.tokenService.verifyToken(params);

      const duration = Date.now() - startTime;
      logger.info(
        {
          curveId: params.curveId,
          exists: result.exists,
          isValid: result.isValid,
          duration,
        },
        'Token verification completed',
      );

      return {
        success: true,
        data: result,
      };
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      const duration = Date.now() - startTime;

      logger.error(
        { error: errorMessage, params, duration },
        'Failed to verify token',
      );

      return {
        success: false,
        error: {
          code: this.getErrorCode(errorMessage),
          message: errorMessage,
          details: params,
        },
      };
    }
  }

  async verifyTokenExists(
    curveId: string,
  ): Promise<VerifyTokenExistenceResponseDto> {
    const startTime = Date.now();

    logger.info({ curveId }, 'Starting token existence verification');

    try {
      // Validate input
      this.validateCurveId(curveId);

      // Check if token exists
      const exists = await this.tokenService.verifyTokenExists(curveId);

      let token;
      if (exists) {
        // Get basic token info if it exists
        const tokenData = await this.tokenService.getTokenByCurveId(curveId);
        if (tokenData) {
          token = {
            id: tokenData.id,
            status: tokenData.status,
            createdAt: tokenData.createdAt.toISOString(),
          };
        }
      }

      const duration = Date.now() - startTime;
      logger.info(
        { curveId, exists, duration },
        'Token existence verification completed',
      );

      return {
        success: true,
        data: {
          exists,
          curveId,
          token,
        },
      };
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      const duration = Date.now() - startTime;

      logger.error(
        { error: errorMessage, curveId, duration },
        'Failed to verify token existence',
      );

      return {
        success: false,
        error: {
          code: this.getErrorCode(errorMessage),
          message: errorMessage,
          details: { curveId },
        },
      };
    }
  }

  private validateVerifyTokenParams(params: VerifyTokenParams): void {
    if (!params.curveId || params.curveId.trim().length === 0) {
      throw new Error('Curve ID is required');
    }

    if (params.creator) {
      // SUI addresses are 64 characters long (32 bytes in hex) with 0x prefix
      if (!/^0x[a-fA-F0-9]{64}$/.test(params.creator)) {
        throw new Error('Invalid SUI address format for creator');
      }
    }

    if (params.ticker) {
      if (params.ticker.length < 1 || params.ticker.length > 10) {
        throw new Error('Ticker must be between 1 and 10 characters');
      }
    }
  }

  private validateCurveId(curveId: string): void {
    if (!curveId || curveId.trim().length === 0) {
      throw new Error('Curve ID is required');
    }
  }

  private getErrorCode(errorMessage: string): string {
    if (
      errorMessage.includes('Curve ID is required') ||
      errorMessage.includes('Ticker must be between') ||
      errorMessage.includes('Invalid SUI address format')
    ) {
      return 'VALIDATION_ERROR';
    }

    if (
      errorMessage.includes('database') ||
      errorMessage.includes('connection')
    ) {
      return 'DATABASE_ERROR';
    }

    return 'TOKEN_VERIFICATION_FAILED';
  }
}
