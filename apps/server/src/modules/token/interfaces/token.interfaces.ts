import type { Network, Token, TokenStatus } from '@hopfun/database';

import type {
  GetTokenResponseDto,
  ListTokensRequest,
  ListTokensResponseDto,
  TokenResponseDto,
} from '../dto/get-token.dto';
import type {
  TokenVerificationResult,
  VerifyTokenExistenceResponseDto,
  VerifyTokenResponseDto,
} from '../dto/verify-token.dto';

export interface ITokenService {
  getTokenById(id: string): Promise<Token | null>;
  getTokenByCurveId(curveId: string): Promise<Token | null>;
  getTokensByCreator(creator: string): Promise<Token[]>;
  listTokens(params: ListTokensParams): Promise<ListTokensResult>;
  verifyTokenExists(curveId: string): Promise<boolean>;
  verifyToken(params: VerifyTokenParams): Promise<TokenVerificationResult>;
}

export interface IGetTokenUseCase {
  getById(id: string): Promise<GetTokenResponseDto>;
  getByCurveId(curveId: string): Promise<GetTokenResponseDto>;
  getByCreator(creator: string): Promise<ListTokensResponseDto>;
  list(params: ListTokensRequest): Promise<ListTokensResponseDto>;
}

export interface IVerifyTokenUseCase {
  verifyToken(params: VerifyTokenParams): Promise<VerifyTokenResponseDto>;
  verifyTokenExists(curveId: string): Promise<VerifyTokenExistenceResponseDto>;
}

export interface ListTokensParams {
  page: number;
  limit: number;
  network?: Network;
  status?: TokenStatus;
  sortBy: 'createdAt' | 'marketCap' | 'currentPrice' | 'lastTrade' | 'volume24h' | 'priceChange24h';
  sortOrder: 'asc' | 'desc';
  // Search and filter
  search?: string;
  filter?: 'all' | 'lastTrade' | 'heatingUp' | 'watchlist' | 'new' | 'graduating';
  userAddress?: string;
  // Range filters
  createdAfter?: string;
  createdBefore?: string;
  minMarketCap?: number;
  maxMarketCap?: number;
}

export interface ListTokensResult {
  tokens: Token[];
  total: number;
}

export interface VerifyTokenParams {
  curveId: string;
  creator?: string;
  ticker?: string;
}

// Re-export TokenVerificationResult from DTO for convenience
export type { TokenVerificationResult } from '../dto/verify-token.dto';

export interface ErrorResponse {
  code: string;
  message: string;
  details?: unknown;
}

export interface TokenConfig {
  database: {
    connectionString: string;
  };
}

// Utility type to convert Prisma Token to DTO
export type TokenToDto = (token: Token) => TokenResponseDto;

// Pagination utility interface
export interface PaginationMeta {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}
