import { z } from 'zod';

export const GetTokenByIdRequestSchema = z.object({
  id: z.string().min(1, 'Token ID is required'),
});

export const GetTokenByCurveIdRequestSchema = z.object({
  curveId: z.string().min(1, 'Curve ID is required'),
});

export const GetTokensByCreatorRequestSchema = z.object({
  creator: z
    .string()
    .min(1, 'Creator address is required')
    .regex(/^0x[a-fA-F0-9]{64}$/, 'Invalid SUI address format'),
});

export const ListTokensRequestSchema = z.object({
  page: z.coerce.number().min(1, 'Page must be at least 1').default(1),
  limit: z.coerce
    .number()
    .min(1)
    .max(100, 'Limit must be between 1 and 100')
    .default(20),
  network: z.enum(['MAINNET', 'TESTNET', 'DEVNET']).optional(),
  status: z.enum(['ACTIVE', 'COMPLETED', 'MIGRATED']).optional(),
  // Search filters
  search: z.string().optional(), // Search by name, symbol, or description
  // Sort options
  sortBy: z
    .enum([
      'createdAt',
      'marketCap',
      'currentPrice',
      'lastTrade',
      'volume24h',
      'priceChange24h'
    ])
    .default('createdAt'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
  // Additional filters
  filter: z
    .enum([
      'all',        // No filter
      'lastTrade',  // Sort by last trade time
      'heatingUp',  // Tokens with high recent activity
      'watchlist',  // User's watchlist (requires user address)
      'new',        // Recently created tokens
      'graduating'  // Tokens close to graduation/migration
    ])
    .default('all'),
  // User address for watchlist filter
  userAddress: z
    .string()
    .regex(/^0x[a-fA-F0-9]{64}$/, 'Invalid SUI address format')
    .optional(),
  // Time range filters
  createdAfter: z.string().datetime().optional(),
  createdBefore: z.string().datetime().optional(),
  // Price range filters
  minMarketCap: z.coerce.number().min(0).optional(),
  maxMarketCap: z.coerce.number().min(0).optional(),
});

export type GetTokenByIdRequest = z.infer<typeof GetTokenByIdRequestSchema>;
export type GetTokenByCurveIdRequest = z.infer<
  typeof GetTokenByCurveIdRequestSchema
>;
export type GetTokensByCreatorRequest = z.infer<
  typeof GetTokensByCreatorRequestSchema
>;
export type ListTokensRequest = z.infer<typeof ListTokensRequestSchema>;

export interface TokenResponseDto {
  id: string;
  curveId: string;
  creator: string;
  coinName: string;
  ticker: string;
  description: string;
  imageUrl?: string;
  twitter: string;
  website: string;
  telegram: string;
  totalSupply: string;
  network: string;
  status: string;
  currentPrice: string;
  marketCap: string;
  marketCapProgress: number; // Percentage progress to graduation (0-100)
  virtualSuiAmount: string;
  suiBalance: string;
  tokenBalance: string;
  availableTokenReserves: string;
  poolId?: string;
  createdAt: string;
  updatedAt: string;
  completedAt?: string;
  migratedAt?: string;
  // Additional fields for comprehensive token data
  contractAddress: string; // The token contract address
  tradeFeePercent: number; // Trading fee percentage
  graduationThreshold: string; // Market cap threshold for graduation
  // Statistics
  stats?: {
    volume24h: string;
    volume7d: string;
    volumeTotal: string;
    transactions24h: number;
    priceHigh24h: string;
    priceLow24h: string;
    priceChange24h: string;
    priceChangePercent24h: string;
    totalHolders: number;
    lastTradeAt?: string;
  };
}

export interface GetTokenResponseDto {
  success: boolean;
  data?: TokenResponseDto;
  error?: {
    code: string;
    message: string;
    details?: unknown;
  };
}

export interface ListTokensResponseDto {
  success: boolean;
  data?: {
    tokens: TokenResponseDto[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
      hasNext: boolean;
      hasPrev: boolean;
    };
  };
  error?: {
    code: string;
    message: string;
    details?: unknown;
  };
}
