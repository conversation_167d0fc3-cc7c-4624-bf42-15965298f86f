import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';

import { app } from '@/app';

// Mock console methods to avoid logs in tests
vi.mock('@hopfun/logger', () => ({
  logger: {
    info: vi.fn(),
    warn: vi.fn(),
    error: vi.fn(),
    debug: vi.fn(),
  },
}));

// Mock the database
vi.mock('@/libs/database', () => ({
  db: {
    token: {
      findUnique: vi.fn(),
      findMany: vi.fn(),
      count: vi.fn(),
    },
  },
}));

describe('Token Routes', () => {
  beforeEach(() => {
    // Mock console methods to avoid logs in tests
    vi.spyOn(console, 'log').mockImplementation(() => {});
    vi.spyOn(console, 'error').mockImplementation(() => {});
    vi.spyOn(console, 'info').mockImplementation(() => {});
    vi.spyOn(console, 'debug').mockImplementation(() => {});
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('GET /api/token/health', () => {
    it('should return health status', async () => {
      const res = await app.request('/api/token/health');
      const body = await res.json();

      expect(res.status).toBe(200);
      expect(body.status).toBe('success');
      expect(body.message).toBe('Token module is healthy');
      expect(body.data).toEqual({
        version: '1.0.0',
        status: 'operational',
      });
    });
  });

  describe('GET /api/token/:id', () => {
    const validId = 'token-id-1';
    const invalidId = '';

    it('should return 404 for empty token ID', async () => {
      const res = await app.request(`/api/token/${invalidId}`);

      // Empty path parameter results in 404 from Hono routing
      expect(res.status).toBe(404);
    });

    it('should handle valid token ID format', async () => {
      const res = await app.request(`/api/token/${validId}`);

      // We expect this to potentially fail with a real database call or succeed if mocked properly
      // The important part is that it passes validation
      expect([200, 404, 500, 503]).toContain(res.status);
    });
  });

  describe('GET /api/token/curve/:curveId', () => {
    const validCurveId = 'curve-id-1';
    const invalidCurveId = '';

    it('should return 404 for empty curve ID', async () => {
      const res = await app.request(`/api/token/curve/${invalidCurveId}`);

      // Empty path parameter results in 404 from Hono routing
      expect(res.status).toBe(404);
    });

    it('should handle valid curve ID format', async () => {
      const res = await app.request(`/api/token/curve/${validCurveId}`);

      // We expect this to potentially fail with a real database call or succeed if mocked properly
      // The important part is that it passes validation
      expect([200, 404, 500, 503]).toContain(res.status);
    });
  });

  describe('GET /api/token/creator/:creator', () => {
    const validCreator = '0x' + 'a'.repeat(64);
    const invalidCreator = 'invalid-address';
    const shortCreator = '0x' + 'a'.repeat(63); // 63 chars instead of 64

    it('should return 400 for invalid creator address format', async () => {
      const res = await app.request(`/api/token/creator/${invalidCreator}`);
      const body = await res.json();

      expect(res.status).toBe(400);
      expect(body.status).toBe('error');
      expect(body.error?.code).toBe('VALIDATION_ERROR');
    });

    it('should return 400 for creator address with wrong length', async () => {
      const res = await app.request(`/api/token/creator/${shortCreator}`);
      const body = await res.json();

      expect(res.status).toBe(400);
      expect(body.status).toBe('error');
      expect(body.error?.code).toBe('VALIDATION_ERROR');
    });

    it('should handle valid creator address format', async () => {
      const res = await app.request(`/api/token/creator/${validCreator}`);

      // We expect this to potentially fail with a real database call or succeed if mocked properly
      // The important part is that it passes validation
      expect([200, 404, 500, 503]).toContain(res.status);
    });

    it('should handle creator address with 0x prefix', async () => {
      const addressWithPrefix = '0x' + 'b'.repeat(64);
      const res = await app.request(`/api/token/creator/${addressWithPrefix}`);

      // Should pass validation at minimum
      expect([200, 404, 500, 503]).toContain(res.status);
    });

    it('should handle special characters in creator parameter', async () => {
      const res = await app.request(
        '/api/token/creator/0x<script>alert(1)</script>',
      );

      // Invalid characters in path result in 404 from Hono routing
      expect(res.status).toBe(404);
    });
  });

  describe('GET /api/token', () => {
    it('should return tokens with default pagination', async () => {
      const res = await app.request('/api/token');

      // We expect this to potentially fail with a real database call or succeed if mocked properly
      // The important part is that it passes validation
      expect([200, 500, 503]).toContain(res.status);
    });

    it('should handle valid pagination parameters', async () => {
      const res = await app.request('/api/token?page=1&limit=10');

      expect([200, 500, 503]).toContain(res.status);
    });

    it('should handle valid filter parameters', async () => {
      const res = await app.request(
        '/api/token?network=TESTNET&status=ACTIVE&sortBy=marketCap&sortOrder=desc',
      );

      expect([200, 500, 503]).toContain(res.status);
    });

    it('should return 400 for invalid page parameter', async () => {
      const res = await app.request('/api/token?page=0');
      const body = await res.json();

      expect(res.status).toBe(400);
      expect(body.status).toBe('error');
      expect(body.error?.code).toBe('VALIDATION_ERROR');
    });

    it('should return 400 for invalid limit parameter', async () => {
      const res = await app.request('/api/token?limit=101'); // Exceeds max limit
      const body = await res.json();

      expect(res.status).toBe(400);
      expect(body.status).toBe('error');
      expect(body.error?.code).toBe('VALIDATION_ERROR');
    });

    it('should return 400 for invalid network parameter', async () => {
      const res = await app.request('/api/token?network=INVALID');
      const body = await res.json();

      expect(res.status).toBe(400);
      expect(body.status).toBe('error');
      expect(body.error?.code).toBe('VALIDATION_ERROR');
    });

    it('should return 400 for invalid status parameter', async () => {
      const res = await app.request('/api/token?status=INVALID');
      const body = await res.json();

      expect(res.status).toBe(400);
      expect(body.status).toBe('error');
      expect(body.error?.code).toBe('VALIDATION_ERROR');
    });

    it('should return 400 for invalid sortBy parameter', async () => {
      const res = await app.request('/api/token?sortBy=invalid');
      const body = await res.json();

      expect(res.status).toBe(400);
      expect(body.status).toBe('error');
      expect(body.error?.code).toBe('VALIDATION_ERROR');
    });

    it('should return 400 for invalid sortOrder parameter', async () => {
      const res = await app.request('/api/token?sortOrder=invalid');
      const body = await res.json();

      expect(res.status).toBe(400);
      expect(body.status).toBe('error');
      expect(body.error?.code).toBe('VALIDATION_ERROR');
    });
  });

  describe('POST /api/token/verify', () => {
    it('should return 500 for missing request body', async () => {
      const res = await app.request('/api/token/verify', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      // Missing body causes JSON parsing error, resulting in 500
      expect(res.status).toBe(500);
    });

    it('should return 400 for empty curve ID', async () => {
      const res = await app.request('/api/token/verify', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          curveId: '',
        }),
      });
      const body = await res.json();

      expect(res.status).toBe(400);
      expect(body.status).toBe('error');
      expect(body.error?.code).toBe('VALIDATION_ERROR');
    });

    it('should return 400 for invalid creator address', async () => {
      const res = await app.request('/api/token/verify', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          curveId: 'curve-id-1',
          creator: 'invalid-address',
        }),
      });
      const body = await res.json();

      expect(res.status).toBe(400);
      expect(body.status).toBe('error');
      expect(body.error?.code).toBe('VALIDATION_ERROR');
    });

    it('should return 400 for invalid ticker length', async () => {
      const res = await app.request('/api/token/verify', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          curveId: 'curve-id-1',
          ticker: 'TOOLONGTICKERHERE', // Too long
        }),
      });
      const body = await res.json();

      expect(res.status).toBe(400);
      expect(body.status).toBe('error');
      expect(body.error?.code).toBe('VALIDATION_ERROR');
    });

    it('should handle valid verification request', async () => {
      const res = await app.request('/api/token/verify', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          curveId: 'curve-id-1',
          creator: '0x' + 'a'.repeat(64),
          ticker: 'TEST',
        }),
      });

      // We expect this to potentially fail with a real database call or succeed if mocked properly
      // The important part is that it passes validation
      expect([200, 500, 503]).toContain(res.status);
    });
  });

  describe('GET /api/token/verify/:curveId', () => {
    const validCurveId = 'curve-id-1';
    const invalidCurveId = '';

    it('should return 404 for empty curve ID', async () => {
      const res = await app.request(`/api/token/verify/${invalidCurveId}`);

      // Empty path parameter results in 404 from Hono routing
      expect(res.status).toBe(404);
    });

    it('should handle valid curve ID format', async () => {
      const res = await app.request(`/api/token/verify/${validCurveId}`);

      // We expect this to potentially fail with a real database call or succeed if mocked properly
      // The important part is that it passes validation
      expect([200, 500, 503]).toContain(res.status);
    });

    it('should handle special characters in curve ID parameter', async () => {
      const res = await app.request(
        '/api/token/verify/<script>alert(1)</script>',
      );

      // Invalid characters in path result in 404 from Hono routing
      expect(res.status).toBe(404);
    });
  });
});
