import type { Token } from '@hopfun/database';
import { logger } from '@hopfun/logger';

import { db } from '@/libs/database';

import type {
  ITokenService,
  ListTokensParams,
  ListTokensResult,
  TokenVerificationResult,
  VerifyTokenParams,
} from '../interfaces/token.interfaces';

export class TokenService implements ITokenService {
  async getTokenById(id: string): Promise<Token | null> {
    try {
      logger.debug({ id }, 'Fetching token by ID');

      const token = await db.token.findUnique({
        where: { id },
        include: {
          stats: true,
        },
      });

      if (!token) {
        logger.debug({ id }, 'Token not found');
        return null;
      }

      logger.debug({ id, ticker: token.ticker }, 'Token found');
      return token;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      logger.error({ error: errorMessage, id }, 'Failed to fetch token by ID');
      throw new Error(`Failed to fetch token: ${errorMessage}`);
    }
  }

  async getTokenByCurveId(curveId: string): Promise<Token | null> {
    try {
      logger.debug({ curveId }, 'Fetching token by curve ID');

      const token = await db.token.findUnique({
        where: { curveId },
        include: {
          stats: true,
        },
      });

      if (!token) {
        logger.debug({ curveId }, 'Token not found');
        return null;
      }

      logger.debug({ curveId, ticker: token.ticker }, 'Token found');
      return token;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      logger.error(
        { error: errorMessage, curveId },
        'Failed to fetch token by curve ID',
      );
      throw new Error(`Failed to fetch token: ${errorMessage}`);
    }
  }

  async getTokensByCreator(creator: string): Promise<Token[]> {
    try {
      logger.debug({ creator }, 'Fetching tokens by creator');

      const tokens = await db.token.findMany({
        where: { creator },
        include: {
          stats: true,
        },
        orderBy: {
          createdAt: 'desc',
        },
      });

      logger.debug({ creator, count: tokens.length }, 'Tokens found');
      return tokens;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      logger.error(
        { error: errorMessage, creator },
        'Failed to fetch tokens by creator',
      );
      throw new Error(`Failed to fetch tokens: ${errorMessage}`);
    }
  }

  async listTokens(params: ListTokensParams): Promise<ListTokensResult> {
    try {
      logger.debug(params, 'Listing tokens with parameters');

      const { 
        page, 
        limit, 
        network, 
        status, 
        sortBy, 
        sortOrder,
        search,
        filter,
        userAddress,
        createdAfter,
        createdBefore,
        minMarketCap,
        maxMarketCap
      } = params;
      const skip = (page - 1) * limit;

      // Build where clause
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const where: any = {};
      if (network) where.network = network;
      if (status) where.status = status;

      // Add search functionality - search in name, ticker, or description
      if (search) {
        where.OR = [
          { coinName: { contains: search, mode: 'insensitive' } },
          { ticker: { contains: search, mode: 'insensitive' } },
          { description: { contains: search, mode: 'insensitive' } },
        ];
      }

      // Add time range filters
      if (createdAfter || createdBefore) {
        where.createdAt = {};
        if (createdAfter) where.createdAt.gte = new Date(createdAfter);
        if (createdBefore) where.createdAt.lte = new Date(createdBefore);
      }

      // Add market cap range filters
      if (minMarketCap !== undefined || maxMarketCap !== undefined) {
        where.marketCap = {};
        if (minMarketCap !== undefined) where.marketCap.gte = String(minMarketCap);
        if (maxMarketCap !== undefined) where.marketCap.lte = String(maxMarketCap);
      }

      // Apply special filters
      const now = new Date();
      const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);
      const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

      switch (filter) {
        case 'new':
          // Tokens created in the last 24 hours
          where.createdAt = { gte: oneDayAgo };
          break;
        case 'heatingUp':
          // Tokens with high recent activity (more than 10 transactions in last 24h)
          // This would require a join with stats table
          break;
        case 'graduating':
          // Tokens close to graduation (market cap > 60K SUI)
          where.marketCap = { gte: '60000000000000' }; // 60K SUI
          where.status = 'ACTIVE';
          break;
        case 'watchlist':
          // This would require a separate watchlist table/feature
          if (userAddress) {
            // TODO: Implement watchlist feature
            logger.warn('Watchlist feature not yet implemented');
          }
          break;
      }

      // Build order by clause
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      let orderBy: any = {};
      
      // Handle special sort cases that require joins
      if (sortBy === 'lastTrade') {
        // Order by last transaction time (would need join with transactions)
        orderBy = { updatedAt: sortOrder };
      } else if (sortBy === 'volume24h' || sortBy === 'priceChange24h') {
        // These require joining with stats table
        orderBy = { 
          stats: { 
            [sortBy]: sortOrder 
          } 
        };
      } else {
        orderBy[sortBy] = sortOrder;
      }

      // Apply lastTrade filter if specified
      if (filter === 'lastTrade') {
        orderBy = { updatedAt: 'desc' };
      }

      // Execute queries in parallel
      const [tokens, total] = await Promise.all([
        db.token.findMany({
          where,
          include: {
            stats: true,
            transactions: {
              orderBy: { createdAt: 'desc' },
              take: 1, // Get last transaction for lastTradeAt
            },
          },
          orderBy,
          skip,
          take: limit,
        }),
        db.token.count({ where }),
      ]);

      // If filtering by heatingUp, post-process the results
      let filteredTokens = tokens;
      if (filter === 'heatingUp') {
        filteredTokens = tokens.filter(token => 
          token.stats && token.stats.transactions24h > 10
        );
      }

      logger.debug(
        {
          count: filteredTokens.length,
          total,
          page,
          limit,
          network,
          status,
          sortBy,
          sortOrder,
          filter,
          search,
        },
        'Tokens listed successfully',
      );

      return { tokens: filteredTokens, total };
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      logger.error({ error: errorMessage, params }, 'Failed to list tokens');
      throw new Error(`Failed to list tokens: ${errorMessage}`);
    }
  }

  async verifyTokenExists(curveId: string): Promise<boolean> {
    try {
      logger.debug({ curveId }, 'Verifying token existence');

      const token = await db.token.findUnique({
        where: { curveId },
        select: { id: true },
      });

      const exists = !!token;
      logger.debug({ curveId, exists }, 'Token existence verified');
      return exists;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      logger.error(
        { error: errorMessage, curveId },
        'Failed to verify token existence',
      );
      throw new Error(`Failed to verify token existence: ${errorMessage}`);
    }
  }

  async verifyToken(
    params: VerifyTokenParams,
  ): Promise<TokenVerificationResult> {
    try {
      logger.debug(params, 'Verifying token with parameters');

      const { curveId, creator, ticker } = params;

      const token = await db.token.findUnique({
        where: { curveId },
        select: {
          id: true,
          curveId: true,
          creator: true,
          coinName: true,
          ticker: true,
          status: true,
          network: true,
          createdAt: true,
        },
      });

      if (!token) {
        logger.debug({ curveId }, 'Token not found during verification');
        return {
          exists: false,
          isValid: false,
          validationErrors: ['Token does not exist'],
        };
      }

      // Perform validation checks
      const validationErrors: string[] = [];

      if (creator && token.creator !== creator) {
        validationErrors.push('Creator address does not match');
      }

      if (ticker && token.ticker !== ticker) {
        validationErrors.push('Ticker does not match');
      }

      const isValid = validationErrors.length === 0;

      logger.debug(
        {
          curveId,
          exists: true,
          isValid,
          validationErrors: validationErrors.length,
        },
        'Token verification completed',
      );

      return {
        exists: true,
        isValid,
        token: {
          id: token.id,
          curveId: token.curveId,
          creator: token.creator,
          coinName: token.coinName,
          ticker: token.ticker,
          status: token.status,
          network: token.network,
          createdAt: token.createdAt.toISOString(),
        },
        validationErrors:
          validationErrors.length > 0 ? validationErrors : undefined,
      };
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      logger.error({ error: errorMessage, params }, 'Failed to verify token');
      throw new Error(`Failed to verify token: ${errorMessage}`);
    }
  }
}
