import { logger } from '@hopfun/logger';
import { Hono } from 'hono';
import { StatusCodes } from 'http-status-codes';

import { formatResponse } from '@/utils/response';

import {
  GetTokenByCurveIdRequestSchema,
  GetTokenByIdRequestSchema,
  GetTokensByCreatorRequestSchema,
  ListTokensRequestSchema,
} from './dto/get-token.dto';
import {
  VerifyTokenExistenceRequestSchema,
  VerifyTokenRequestSchema,
} from './dto/verify-token.dto';
import { GetTokenUseCase } from './use-cases/get-token.use-case';
import { VerifyTokenUseCase } from './use-cases/verify-token.use-case';

const token = new Hono()
  .get('/health', (c) => {
    return formatResponse(c, StatusCodes.OK, {
      message: 'Token module is healthy',
      data: {
        version: '1.0.0',
        status: 'operational',
      },
    });
  })
  .get('/:id', async (c) => {
    try {
      // Get ID from path parameters
      const id = c.req.param('id');

      // Validate request
      const validationResult = GetTokenByIdRequestSchema.safeParse({ id });

      if (!validationResult.success) {
        logger.warn(
          {
            errors: validationResult.error.issues,
            id,
          },
          'Invalid get token by ID request',
        );

        return formatResponse(c, StatusCodes.BAD_REQUEST, {
          error: {
            code: 'VALIDATION_ERROR',
            details: validationResult.error.issues,
          },
        });
      }

      // Execute use case
      const getTokenUseCase = new GetTokenUseCase();
      const result = await getTokenUseCase.getById(validationResult.data.id);

      if (result.success) {
        return formatResponse(c, StatusCodes.OK, {
          message: 'Token retrieved successfully',
          data: result.data,
        });
      } else {
        // Determine status code based on error code
        let statusCode = StatusCodes.INTERNAL_SERVER_ERROR;

        if (result.error?.code === 'TOKEN_NOT_FOUND') {
          statusCode = StatusCodes.NOT_FOUND;
        } else if (result.error?.code === 'VALIDATION_ERROR') {
          statusCode = StatusCodes.BAD_REQUEST;
        } else if (result.error?.code === 'DATABASE_ERROR') {
          statusCode = StatusCodes.SERVICE_UNAVAILABLE;
        }

        return formatResponse(c, statusCode, {
          message: result.error?.message,
          error: {
            code: result.error?.code ?? 'UNKNOWN_ERROR',
            details: result.error?.details,
          },
        });
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      logger.error(
        { error: errorMessage },
        'Unexpected error in get token by ID',
      );

      return formatResponse(c, StatusCodes.INTERNAL_SERVER_ERROR, {
        message: 'Internal server error',
        error: {
          code: 'INTERNAL_ERROR',
        },
      });
    }
  })
  .get('/curve/:curveId', async (c) => {
    try {
      // Get curve ID from path parameters
      const curveId = c.req.param('curveId');

      // Validate request
      const validationResult = GetTokenByCurveIdRequestSchema.safeParse({
        curveId,
      });

      if (!validationResult.success) {
        logger.warn(
          {
            errors: validationResult.error.issues,
            curveId,
          },
          'Invalid get token by curve ID request',
        );

        return formatResponse(c, StatusCodes.BAD_REQUEST, {
          error: {
            code: 'VALIDATION_ERROR',
            details: validationResult.error.issues,
          },
        });
      }

      // Execute use case
      const getTokenUseCase = new GetTokenUseCase();
      const result = await getTokenUseCase.getByCurveId(
        validationResult.data.curveId,
      );

      if (result.success) {
        return formatResponse(c, StatusCodes.OK, {
          message: 'Token retrieved successfully',
          data: result.data,
        });
      } else {
        // Determine status code based on error code
        let statusCode = StatusCodes.INTERNAL_SERVER_ERROR;

        if (result.error?.code === 'TOKEN_NOT_FOUND') {
          statusCode = StatusCodes.NOT_FOUND;
        } else if (result.error?.code === 'VALIDATION_ERROR') {
          statusCode = StatusCodes.BAD_REQUEST;
        } else if (result.error?.code === 'DATABASE_ERROR') {
          statusCode = StatusCodes.SERVICE_UNAVAILABLE;
        }

        return formatResponse(c, statusCode, {
          message: result.error?.message,
          error: {
            code: result.error?.code ?? 'UNKNOWN_ERROR',
            details: result.error?.details,
          },
        });
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      logger.error(
        { error: errorMessage },
        'Unexpected error in get token by curve ID',
      );

      return formatResponse(c, StatusCodes.INTERNAL_SERVER_ERROR, {
        message: 'Internal server error',
        error: {
          code: 'INTERNAL_ERROR',
        },
      });
    }
  })
  .get('/creator/:creator', async (c) => {
    try {
      // Get creator from path parameters
      const creator = c.req.param('creator');

      // Validate request
      const validationResult = GetTokensByCreatorRequestSchema.safeParse({
        creator,
      });

      if (!validationResult.success) {
        logger.warn(
          {
            errors: validationResult.error.issues,
            creator,
          },
          'Invalid get tokens by creator request',
        );

        return formatResponse(c, StatusCodes.BAD_REQUEST, {
          error: {
            code: 'VALIDATION_ERROR',
            details: validationResult.error.issues,
          },
        });
      }

      // Execute use case
      const getTokenUseCase = new GetTokenUseCase();
      const result = await getTokenUseCase.getByCreator(
        validationResult.data.creator,
      );

      if (result.success) {
        return formatResponse(c, StatusCodes.OK, {
          message: 'Tokens retrieved successfully',
          data: result.data,
        });
      } else {
        // Determine status code based on error code
        let statusCode = StatusCodes.INTERNAL_SERVER_ERROR;

        if (
          result.error?.code === 'VALIDATION_ERROR' ||
          result.error?.code === 'INVALID_ADDRESS_FORMAT'
        ) {
          statusCode = StatusCodes.BAD_REQUEST;
        } else if (result.error?.code === 'DATABASE_ERROR') {
          statusCode = StatusCodes.SERVICE_UNAVAILABLE;
        }

        return formatResponse(c, statusCode, {
          message: result.error?.message,
          error: {
            code: result.error?.code ?? 'UNKNOWN_ERROR',
            details: result.error?.details,
          },
        });
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      logger.error(
        { error: errorMessage },
        'Unexpected error in get tokens by creator',
      );

      return formatResponse(c, StatusCodes.INTERNAL_SERVER_ERROR, {
        message: 'Internal server error',
        error: {
          code: 'INTERNAL_ERROR',
        },
      });
    }
  })
  .get('/', async (c) => {
    try {
      // Get query parameters
      const page = c.req.query('page');
      const limit = c.req.query('limit');
      const network = c.req.query('network');
      const status = c.req.query('status');
      const sortBy = c.req.query('sortBy');
      const sortOrder = c.req.query('sortOrder');
      const search = c.req.query('search');
      const filter = c.req.query('filter');
      const userAddress = c.req.query('userAddress');
      const createdAfter = c.req.query('createdAfter');
      const createdBefore = c.req.query('createdBefore');
      const minMarketCap = c.req.query('minMarketCap');
      const maxMarketCap = c.req.query('maxMarketCap');

      // Validate request
      const validationResult = ListTokensRequestSchema.safeParse({
        page,
        limit,
        network,
        status,
        sortBy,
        sortOrder,
        search,
        filter,
        userAddress,
        createdAfter,
        createdBefore,
        minMarketCap,
        maxMarketCap,
      });

      if (!validationResult.success) {
        logger.warn(
          {
            errors: validationResult.error.issues,
            query: { 
              page, limit, network, status, sortBy, sortOrder,
              search, filter, userAddress, createdAfter, createdBefore,
              minMarketCap, maxMarketCap 
            },
          },
          'Invalid list tokens request',
        );

        return formatResponse(c, StatusCodes.BAD_REQUEST, {
          error: {
            code: 'VALIDATION_ERROR',
            details: validationResult.error.issues,
          },
        });
      }

      // Execute use case
      const getTokenUseCase = new GetTokenUseCase();
      const result = await getTokenUseCase.list(validationResult.data);

      if (result.success) {
        return formatResponse(c, StatusCodes.OK, {
          message: 'Tokens listed successfully',
          data: result.data,
        });
      } else {
        // Determine status code based on error code
        let statusCode = StatusCodes.INTERNAL_SERVER_ERROR;

        if (result.error?.code === 'VALIDATION_ERROR') {
          statusCode = StatusCodes.BAD_REQUEST;
        } else if (result.error?.code === 'DATABASE_ERROR') {
          statusCode = StatusCodes.SERVICE_UNAVAILABLE;
        }

        return formatResponse(c, statusCode, {
          message: result.error?.message,
          error: {
            code: result.error?.code ?? 'UNKNOWN_ERROR',
            details: result.error?.details,
          },
        });
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      logger.error({ error: errorMessage }, 'Unexpected error in list tokens');

      return formatResponse(c, StatusCodes.INTERNAL_SERVER_ERROR, {
        message: 'Internal server error',
        error: {
          code: 'INTERNAL_ERROR',
        },
      });
    }
  })
  .post('/verify', async (c) => {
    try {
      // Get request body
      const body = await c.req.json();

      // Validate request
      const validationResult = VerifyTokenRequestSchema.safeParse(body);

      if (!validationResult.success) {
        logger.warn(
          {
            errors: validationResult.error.issues,
            body,
          },
          'Invalid verify token request',
        );

        return formatResponse(c, StatusCodes.BAD_REQUEST, {
          error: {
            code: 'VALIDATION_ERROR',
            details: validationResult.error.issues,
          },
        });
      }

      // Execute use case
      const verifyTokenUseCase = new VerifyTokenUseCase();
      const result = await verifyTokenUseCase.verifyToken(
        validationResult.data,
      );

      if (result.success) {
        return formatResponse(c, StatusCodes.OK, {
          message: 'Token verification completed',
          data: result.data,
        });
      } else {
        // Determine status code based on error code
        let statusCode = StatusCodes.INTERNAL_SERVER_ERROR;

        if (result.error?.code === 'VALIDATION_ERROR') {
          statusCode = StatusCodes.BAD_REQUEST;
        } else if (result.error?.code === 'DATABASE_ERROR') {
          statusCode = StatusCodes.SERVICE_UNAVAILABLE;
        }

        return formatResponse(c, statusCode, {
          message: result.error?.message,
          error: {
            code: result.error?.code ?? 'UNKNOWN_ERROR',
            details: result.error?.details,
          },
        });
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      logger.error({ error: errorMessage }, 'Unexpected error in verify token');

      return formatResponse(c, StatusCodes.INTERNAL_SERVER_ERROR, {
        message: 'Internal server error',
        error: {
          code: 'INTERNAL_ERROR',
        },
      });
    }
  })
  .get('/verify/:curveId', async (c) => {
    try {
      // Get curve ID from path parameters
      const curveId = c.req.param('curveId');

      // Validate request
      const validationResult = VerifyTokenExistenceRequestSchema.safeParse({
        curveId,
      });

      if (!validationResult.success) {
        logger.warn(
          {
            errors: validationResult.error.issues,
            curveId,
          },
          'Invalid verify token existence request',
        );

        return formatResponse(c, StatusCodes.BAD_REQUEST, {
          error: {
            code: 'VALIDATION_ERROR',
            details: validationResult.error.issues,
          },
        });
      }

      // Execute use case
      const verifyTokenUseCase = new VerifyTokenUseCase();
      const result = await verifyTokenUseCase.verifyTokenExists(
        validationResult.data.curveId,
      );

      if (result.success) {
        return formatResponse(c, StatusCodes.OK, {
          message: 'Token existence verification completed',
          data: result.data,
        });
      } else {
        // Determine status code based on error code
        let statusCode = StatusCodes.INTERNAL_SERVER_ERROR;

        if (result.error?.code === 'VALIDATION_ERROR') {
          statusCode = StatusCodes.BAD_REQUEST;
        } else if (result.error?.code === 'DATABASE_ERROR') {
          statusCode = StatusCodes.SERVICE_UNAVAILABLE;
        }

        return formatResponse(c, statusCode, {
          message: result.error?.message,
          error: {
            code: result.error?.code ?? 'UNKNOWN_ERROR',
            details: result.error?.details,
          },
        });
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      logger.error(
        { error: errorMessage },
        'Unexpected error in verify token existence',
      );

      return formatResponse(c, StatusCodes.INTERNAL_SERVER_ERROR, {
        message: 'Internal server error',
        error: {
          code: 'INTERNAL_ERROR',
        },
      });
    }
  });

export { token };
