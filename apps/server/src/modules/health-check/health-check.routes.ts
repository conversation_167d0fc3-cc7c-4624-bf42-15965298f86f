import { logger } from '@hopfun/logger';
import { Hono } from 'hono';
import { StatusCodes } from 'http-status-codes';

import { db } from '@/libs/database';
import { formatResponse } from '@/utils/response';

const healthCheck = new Hono()
  .get('/liveness', (c) => {
    try {
      return formatResponse(c, StatusCodes.OK, {
        message: 'Server is running',
      });
    } catch (error) {
      logger.error({ error }, 'Liveness health check failed');
      throw error;
    }
  })
  .get('/readiness', async (c) => {
    try {
      await db.$connect();
      logger.info('Database connection established successfully');
      return formatResponse(c, StatusCodes.OK, {
        message: 'Database is ready',
      });
    } catch (error) {
      logger.error({ error }, 'Readiness health check failed');
      throw error;
    }
  });

export { healthCheck };
