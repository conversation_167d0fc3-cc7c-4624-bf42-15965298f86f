#!/bin/bash

echo "🔍 Testing BondingCurveCreated event queries..."

RPC_URL="https://fullnode.devnet.sui.io"
HOPFUN_PACKAGE="0x497ff2769b28664d052601132542a2a77e75339945dc34a494e40fcd680065ac"

# Test 1: Query for BondingCurveCreated events without generic type
echo "1️⃣ Querying for BondingCurveCreated events (without generic)..."
curl -s -X POST "$RPC_URL" \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "id": 1,
    "method": "suix_queryEvents",
    "params": [
      {
        "MoveEventType": "'$HOPFUN_PACKAGE'::events::BondingCurveCreated"
      },
      null,
      5,
      true
    ]
  }' | jq '.result.data | length' | xargs -I {} echo "📊 Found {} BondingCurveCreated events (without generic)"

# Test 2: Query for events from the specific transaction we know has BondingCurveCreated
echo ""
echo "2️⃣ Querying events from specific transaction..."
TX_DIGEST="8h92uSdBYsarWqdZej2aDMpxFrxLh3hg6UgsxFHG16rY"
curl -s -X POST "$RPC_URL" \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "id": 2,
    "method": "sui_getTransactionBlock",
    "params": [
      "'$TX_DIGEST'",
      {
        "showEvents": true
      }
    ]
  }' | jq '.result.events[] | {
    type: .type,
    packageId: .packageId,
    sender: .sender
  }'

# Test 3: Query for all events from HopFun package using MoveModule filter
echo ""
echo "3️⃣ Querying all events from HopFun package using MoveModule filter..."
curl -s -X POST "$RPC_URL" \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "id": 3,
    "method": "suix_queryEvents",
    "params": [
      {
        "MoveModule": {
          "package": "'$HOPFUN_PACKAGE'",
          "module": "events"
        }
      },
      null,
      10,
      true
    ]
  }' | jq '.result.data | length' | xargs -I {} echo "📊 Found {} events from HopFun events module"

# Test 4: Show the actual events from HopFun package
echo ""
echo "4️⃣ Showing actual events from HopFun package..."
curl -s -X POST "$RPC_URL" \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "id": 4,
    "method": "suix_queryEvents",
    "params": [
      {
        "MoveModule": {
          "package": "'$HOPFUN_PACKAGE'",
          "module": "events"
        }
      },
      null,
      10,
      true
    ]
  }' | jq '.result.data[] | {
    type: .type,
    packageId: .packageId,
    timestampMs: .timestampMs
  }'

echo ""
echo "🏁 BondingCurveCreated event query test complete!"
