# MongoDB Configuration File
# For MongoDB 7.0

# Storage Configuration
storage:
  dbPath: /data/db
  journal:
    enabled: true
  directoryPerDB: true
  engine: wiredTiger
  wiredTiger:
    engineConfig:
      journalCompressor: snappy
      directoryForIndexes: true
    collectionConfig:
      blockCompressor: snappy
    indexConfig:
      prefixCompression: true

# System Log Configuration
systemLog:
  destination: file
  logAppend: true
  path: /var/log/mongodb/mongod.log
  logRotate: reopen
  verbosity: 0
  component:
    accessControl:
      verbosity: 0
    command:
      verbosity: 0

# Network Configuration
net:
  port: 27017
  bindIp: 0.0.0.0
  maxIncomingConnections: 1000
  compression:
    compressors: snappy,zstd,zlib

# Security Configuration
security:
  authorization: enabled
  javascriptEnabled: true

# Operation Profiling
operationProfiling:
  mode: slowOp
  slowOpThresholdMs: 100
  slowOpSampleRate: 1.0

# Replication Configuration
replication:
  oplogSizeMB: 2048

# Set Parameters
setParameter:
  enableLocalhostAuthBypass: false
  authenticationMechanisms: SCRAM-SHA-256,SCRAM-SHA-1
  failIndexKeyTooLong: false
  diagnosticDataCollectionEnabled: false
  ttlMonitorSleepSecs: 60
  cursorTimeoutMillis: 600000
  maxIndexBuildMemoryUsageMegabytes: 500