# **HopFun - Meme Coin Trading Platform PRD**
*A comprehensive SUI blockchain-based meme coin creation and trading ecosystem*

## **1. Executive Summary**

### **1.1 Vision**
Create the premier meme coin trading platform on SUI blockchain, enabling users to easily create, discover, and trade meme coins with real-time market data and seamless wallet integration.

### **1.2 Core Value Proposition**
- **Instant Coin Creation**: Deploy meme coins on SUI in seconds
- **Real-time Trading**: Live price feeds and instant transactions
- **Community-Driven**: Social features and community governance
- **Low Fees**: Leverage SUI's low transaction costs
- **Professional Tools**: Advanced trading features and analytics

---

## **2. Technical Architecture**

### **2.1 System Overview**
```mermaid
graph TB
    A[Frontend - Next.js] --> B[API Gateway]
    B --> C[Backend Services]
    C --> D[Database - MongoDB]
    C --> E[Indexer Service]
    E --> F[SUI Blockchain]
    A --> G[SUI Wallet Integration]
    G --> F
    C --> H[Real-time WebSocket]
    H --> A
```

### **2.2 Core Components**

#### **Frontend (Next.js)**
- **Technology**: Next.js 14, TypeScript, TailwindCSS
- **Wallet Integration**: SUI Wallet Kit
- **State Management**: Zustand/Redux Toolkit
- **Real-time**: WebSocket connections

#### **Backend Services**
- **API Server**: Node.js/Express with TypeScript
- **Database**: MongoDB with Prisma ORM
- **Indexer**: Real-time blockchain event processor
- **WebSocket Server**: Live price feeds and notifications

#### **Blockchain Integration**
- **Network**: SUI Blockchain (Devnet → Mainnet)
- **Smart Contracts**: Move language
- **Event Processing**: Real-time indexing system

---

## **3. Feature Specifications**

### **3.1 Core Features**

#### **🪙 Coin Creation & Deployment**
**Priority**: P0 (Critical)
**Status**: ✅ Implemented

**Features**:
- One-click meme coin deployment
- Customizable token metadata (name, symbol, description, image)
- Social links integration (Twitter, Telegram, Website)
- Bonding curve mechanism for price discovery
- Automatic liquidity provision

**Technical Requirements**:
- Smart contract deployment on SUI
- IPFS integration for metadata storage
- Gas optimization for deployment costs
- Event emission for indexer tracking

#### **📊 Real-time Indexing System**
**Priority**: P0 (Critical)  
**Status**: ✅ Fixed & Operational

**Features**:
- Real-time blockchain event detection
- Automatic coin metadata extraction
- Price and volume tracking
- Transaction history indexing
- Market cap calculations

**Technical Requirements**:
- Event subscription to SUI RPC
- MongoDB storage with optimized queries
- Cursor-based pagination for events
- Error handling and retry mechanisms
- Historical data backfill

#### **🔍 Coin Discovery & Listing**
**Priority**: P0 (Critical)
**Status**: 🔄 In Development

**Features**:
- Trending coins dashboard
- Search and filtering capabilities
- Sorting by market cap, volume, age
- Featured coin promotions
- Category-based browsing

**API Endpoints**:
```typescript
GET /api/coins - List all coins with pagination
GET /api/coins/trending - Get trending coins
GET /api/coins/search?q={query} - Search coins
GET /api/coins/{id} - Get specific coin details
```

#### **💹 Trading Interface**
**Priority**: P0 (Critical)
**Status**: 🔄 Planned

**Features**:
- Buy/sell interface with price impact calculation
- Slippage protection
- Transaction confirmation dialogs
- Real-time price updates
- Order history tracking

**Technical Requirements**:
- SUI wallet integration for transactions
- Price calculation algorithms
- MEV protection mechanisms
- Transaction status tracking

#### **👤 Portfolio Management**
**Priority**: P1 (High)
**Status**: 🔄 Planned

**Features**:
- User coin holdings display
- Portfolio value tracking
- P&L calculations
- Transaction history
- Performance analytics

**Database Schema**:
```typescript
interface UserHolding {
  userId: string;
  tokenId: string;
  balance: string;
  averageBuyPrice: string;
  totalInvested: string;
  unrealizedPnL: string;
}
```

### **3.2 Advanced Features**

#### **📈 Advanced Analytics**
**Priority**: P2 (Medium)
**Status**: 🔄 Future

**Features**:
- Price charts with technical indicators
- Volume analysis
- Holder distribution charts
- Market sentiment indicators
- Comparative analysis tools

#### **🤝 Social Features**
**Priority**: P2 (Medium)
**Status**: 🔄 Future

**Features**:
- User profiles and reputation system
- Coin comments and ratings
- Social trading features
- Community governance voting
- Referral programs

#### **🔔 Notifications & Alerts**
**Priority**: P2 (Medium)
**Status**: 🔄 Future

**Features**:
- Price alerts
- New coin launch notifications
- Portfolio performance alerts
- Transaction confirmations
- Market news integration

---

## **4. Development Phases & Roadmap**

### **Phase 1: Foundation (Weeks 1-4) ✅ COMPLETE**
- [x] Smart contract development and deployment
- [x] Basic frontend coin creation interface
- [x] Indexer service implementation
- [x] Database schema design
- [x] Core API endpoints

### **Phase 2: Core Trading (Weeks 5-8) 🔄 IN PROGRESS**
- [ ] Trading interface implementation
- [ ] Wallet integration improvements
- [ ] Real-time price feeds
- [ ] Portfolio tracking
- [ ] Transaction history

**Current Sprint Tasks**:
1. **Fix indexer BondingCurveCreated event detection** ✅ DONE
2. **Implement coin listing API** (In Progress)
3. **Build trading interface components** (Next)
4. **Add real-time WebSocket connections** (Next)

### **Phase 3: Enhanced UX (Weeks 9-12)**
- [ ] Advanced search and filtering
- [ ] Price charts and analytics
- [ ] Mobile responsiveness
- [ ] Performance optimizations
- [ ] Security audits

### **Phase 4: Social & Community (Weeks 13-16)**
- [ ] User profiles and authentication
- [ ] Social features implementation
- [ ] Community governance tools
- [ ] Referral system
- [ ] Advanced notifications

### **Phase 5: Scale & Optimize (Weeks 17-20)**
- [ ] Mainnet deployment
- [ ] Performance monitoring
- [ ] Advanced analytics
- [ ] Mobile app development
- [ ] Partnership integrations

---

## **5. Technical Implementation Details**

### **5.1 Database Schema**

#### **Core Models**
```typescript
// Token Model
interface Token {
  id: string;
  curveId: string;
  creator: string;
  coinName: string;
  ticker: string;
  description: string;
  imageUrl?: string;
  socialLinks: {
    twitter: string;
    website: string;
    telegram: string;
  };
  totalSupply: string;
  currentPrice: string;
  marketCap: string;
  volume24h: string;
  status: 'ACTIVE' | 'COMPLETED' | 'MIGRATED';
  createdAt: Date;
  updatedAt: Date;
}

// User Holdings
interface UserHolding {
  id: string;
  userId: string;
  tokenId: string;
  balance: string;
  averageBuyPrice: string;
  totalInvested: string;
  createdAt: Date;
  updatedAt: Date;
}

// Transaction History
interface TokenTransaction {
  id: string;
  tokenId: string;
  userId: string;
  type: 'BUY' | 'SELL';
  suiAmount: string;
  tokenAmount: string;
  price: string;
  transactionHash: string;
  createdAt: Date;
}
```

### **5.2 API Specifications**

#### **Coin Management APIs**
```typescript
// Get all coins with pagination and filtering
GET /api/coins?page=1&limit=20&sort=marketCap&order=desc&status=ACTIVE

// Get trending coins (top by volume/price change)
GET /api/coins/trending?timeframe=24h&limit=10

// Search coins by name or ticker
GET /api/coins/search?q=doge&limit=10

// Get specific coin details
GET /api/coins/{curveId}

// Get coin price history
GET /api/coins/{curveId}/price-history?timeframe=24h&interval=1h
```

#### **Trading APIs**
```typescript
// Get quote for buying/selling
POST /api/trading/quote
{
  "curveId": "0x...",
  "type": "BUY" | "SELL",
  "amount": "1000000", // in SUI or tokens
  "slippage": "0.5" // percentage
}

// Execute trade
POST /api/trading/execute
{
  "curveId": "0x...",
  "type": "BUY" | "SELL",
  "amount": "1000000",
  "maxSlippage": "0.5",
  "transactionHash": "0x..."
}
```

#### **Portfolio APIs**
```typescript
// Get user portfolio
GET /api/portfolio/{userId}

// Get user transaction history
GET /api/portfolio/{userId}/transactions?page=1&limit=20

// Get user holdings for specific token
GET /api/portfolio/{userId}/holdings/{tokenId}
```

### **5.3 Real-time Features**

#### **WebSocket Events**
```typescript
// Price updates
interface PriceUpdateEvent {
  type: 'PRICE_UPDATE';
  curveId: string;
  price: string;
  change24h: string;
  volume24h: string;
  timestamp: number;
}

// New coin launches
interface NewCoinEvent {
  type: 'NEW_COIN';
  coin: Token;
  timestamp: number;
}

// Trade notifications
interface TradeEvent {
  type: 'TRADE';
  curveId: string;
  type: 'BUY' | 'SELL';
  amount: string;
  price: string;
  timestamp: number;
}
```

---

## **6. Security & Compliance**

### **6.1 Smart Contract Security**
- [ ] Multi-signature admin controls
- [ ] Reentrancy protection
- [ ] Integer overflow protection
- [ ] Access control mechanisms
- [ ] Emergency pause functionality

### **6.2 Frontend Security**
- [ ] Input validation and sanitization
- [ ] XSS protection
- [ ] CSRF protection
- [ ] Secure wallet connections
- [ ] Rate limiting

### **6.3 Backend Security**
- [ ] API authentication and authorization
- [ ] Database query protection
- [ ] Environment variable security
- [ ] Logging and monitoring
- [ ] Error handling without information leakage

---

## **7. Performance & Scalability**

### **7.1 Performance Targets**
- **Page Load Time**: < 2 seconds
- **API Response Time**: < 500ms (95th percentile)
- **Real-time Updates**: < 100ms latency
- **Transaction Confirmation**: < 5 seconds
- **Concurrent Users**: 10,000+

### **7.2 Scalability Strategy**
- **Database**: MongoDB sharding and read replicas
- **Caching**: Redis for frequently accessed data
- **CDN**: Static asset delivery optimization
- **Load Balancing**: Horizontal scaling of API servers
- **Indexer**: Multiple indexer instances for redundancy

---

## **8. Testing Strategy**

### **8.1 Testing Pyramid**
```
    /\
   /  \    E2E Tests (10%)
  /____\   Integration Tests (20%)
 /______\  Unit Tests (70%)
```

### **8.2 Test Categories**
- **Unit Tests**: Individual component/function testing
- **Integration Tests**: API endpoint and database testing
- **E2E Tests**: Full user journey testing
- **Smart Contract Tests**: Move language testing
- **Performance Tests**: Load and stress testing

---

## **9. Monitoring & Analytics**

### **9.1 Application Monitoring**
- **Error Tracking**: Sentry integration
- **Performance Monitoring**: Application metrics
- **Uptime Monitoring**: Service availability tracking
- **Log Aggregation**: Centralized logging system

### **9.2 Business Analytics**
- **User Engagement**: DAU/MAU tracking
- **Trading Volume**: Platform transaction metrics
- **Coin Performance**: Success rate tracking
- **Revenue Metrics**: Fee collection analysis

---

## **10. Deployment & DevOps**

### **10.1 Infrastructure**
- **Cloud Provider**: AWS/GCP
- **Container Orchestration**: Docker + Kubernetes
- **Database**: MongoDB Atlas
- **CDN**: CloudFlare
- **Monitoring**: DataDog/New Relic

### **10.2 CI/CD Pipeline**
```yaml
# GitHub Actions Workflow
name: Deploy to Production
on:
  push:
    branches: [main]
jobs:
  test:
    - Unit tests
    - Integration tests
    - Security scans
  build:
    - Docker image build
    - Push to registry
  deploy:
    - Deploy to staging
    - Run E2E tests
    - Deploy to production
```

---

## **11. Success Metrics & KPIs**

### **11.1 Technical KPIs**
- **System Uptime**: 99.9%
- **API Response Time**: < 500ms
- **Error Rate**: < 0.1%
- **Event Processing Latency**: < 1 second

### **11.2 Business KPIs**
- **Daily Active Users**: Target 1,000+ by Month 3
- **Coins Created**: Target 100+ per day
- **Trading Volume**: Target $1M+ per month
- **User Retention**: 30% monthly retention rate

---

## **12. Risk Assessment & Mitigation**

### **12.1 Technical Risks**
| Risk | Impact | Probability | Mitigation |
|------|--------|-------------|------------|
| Smart Contract Bugs | High | Medium | Comprehensive testing, audits |
| Indexer Downtime | High | Low | Redundant indexers, monitoring |
| Database Performance | Medium | Medium | Optimization, scaling |
| SUI Network Issues | High | Low | Fallback mechanisms |

### **12.2 Business Risks**
| Risk | Impact | Probability | Mitigation |
|------|--------|-------------|------------|
| Regulatory Changes | High | Medium | Legal compliance, monitoring |
| Competition | Medium | High | Feature differentiation |
| Market Volatility | Medium | High | Risk warnings, education |
| User Adoption | High | Medium | Marketing, user experience |

---

## **13. Next Immediate Actions**

### **🚀 Week 1 Priorities**
1. **✅ COMPLETE**: Fix indexer event detection (BondingCurveCreated events)
2. **🔄 IN PROGRESS**: Implement coin listing API with proper pagination
3. **📋 NEXT**: Build trading interface components
4. **📋 NEXT**: Add real-time WebSocket price feeds
5. **📋 NEXT**: Create portfolio tracking functionality

### **🎯 Success Criteria for Week 1**
- [ ] All coin creation events properly indexed in database
- [ ] Coin listing API returns accurate data with < 500ms response time
- [ ] Trading interface mockups approved
- [ ] WebSocket connection established for real-time updates

---

**The indexer issue has been resolved and the platform is now ready for the next phase of development. The foundation is solid and we can proceed with building the trading interface and real-time features.**
