// Simple test for event queries
import { SuiClient } from '@mysten/sui/client';

const client = new SuiClient({
  url: 'https://fullnode.devnet.sui.io',
});

const hopfunPackageId =
  '0x497ff2769b28664d052601132542a2a77e75339945dc34a494e40fcd680065ac';

async function testQueries() {
  console.log('🧪 Testing different query approaches...');

  try {
    console.log('\n1️⃣ Testing MoveModule filter...');
    const moduleEventsResponse = await client.queryEvents({
      query: {
        MoveModule: {
          package: hopfunPackageId,
          module: 'events',
        },
      },
      limit: 10,
      order: 'descending',
    });
    console.log('📊 MoveModule results:', moduleEventsResponse.data.length);

    console.log('\n2️⃣ Testing ConnectorCreated event type...');
    const connectorEventsResponse = await client.queryEvents({
      query: {
        MoveEventType: `${hopfunPackageId}::events::ConnectorCreated`,
      },
      limit: 10,
      order: 'descending',
    });
    console.log(
      '📊 ConnectorCreated results:',
      connectorEventsResponse.data.length,
    );

    if (connectorEventsResponse.data.length > 0) {
      console.log('✅ Found ConnectorCreated events!');
      const firstEvent = connectorEventsResponse.data[0];
      console.log('📄 First event:', {
        type: firstEvent.type,
        transactionDigest: firstEvent.id.txDigest,
        timestampMs: firstEvent.timestampMs,
      });
    }

    console.log('\n3️⃣ Testing BondingCurveCreated with SUI type...');
    const bondingCurveEventsResponse = await client.queryEvents({
      query: {
        MoveEventType: `${hopfunPackageId}::events::BondingCurveCreated<0x2::sui::SUI>`,
      },
      limit: 10,
      order: 'descending',
    });
    console.log(
      '📊 BondingCurveCreated<SUI> results:',
      bondingCurveEventsResponse.data.length,
    );

    if (bondingCurveEventsResponse.data.length > 0) {
      console.log('✅ Found BondingCurveCreated events!');
      const firstEvent = bondingCurveEventsResponse.data[0];
      console.log('📄 First event:', {
        type: firstEvent.type,
        transactionDigest: firstEvent.id.txDigest,
        timestampMs: firstEvent.timestampMs,
      });
    }

    console.log('\n4️⃣ Testing BondingCurveCreated without type parameter...');
    const bondingCurveGenericResponse = await client.queryEvents({
      query: {
        MoveEventType: `${hopfunPackageId}::events::BondingCurveCreated`,
      },
      limit: 10,
      order: 'descending',
    });
    console.log(
      '📊 BondingCurveCreated (generic) results:',
      bondingCurveGenericResponse.data.length,
    );

    if (bondingCurveGenericResponse.data.length > 0) {
      console.log('✅ Found generic BondingCurveCreated events!');
      const firstEvent = bondingCurveGenericResponse.data[0];
      console.log('📄 First event:', {
        type: firstEvent.type,
        transactionDigest: firstEvent.id.txDigest,
        timestampMs: firstEvent.timestampMs,
      });
    }

    console.log('\n5️⃣ Testing all events from the package...');
    const allPackageEventsResponse = await client.queryEvents({
      query: {
        Package: hopfunPackageId,
      },
      limit: 20,
      order: 'descending',
    });
    console.log(
      '📊 All package events results:',
      allPackageEventsResponse.data.length,
    );

    if (allPackageEventsResponse.data.length > 0) {
      console.log('✅ Found package events!');
      const eventTypes = new Set();
      for (const event of allPackageEventsResponse.data) {
        eventTypes.add(event.type);
      }
      console.log('📋 Unique event types found:');
      for (const eventType of eventTypes) {
        console.log(`  - ${eventType}`);
      }
    }
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

testQueries();
