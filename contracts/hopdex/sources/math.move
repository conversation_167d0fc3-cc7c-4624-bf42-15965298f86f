module hopdex::math {

    public fun calculate_amount_out(
        amount_in: u64,
        reserve_in: u64,
        reserve_out: u64,
    ): u64 {
        // S<PERSON> is input: fee on amount_in
        let amount_in_with_fee = (amount_in as u128);
        let numerator = amount_in_with_fee * (reserve_out as u128);
        let denominator = (reserve_in as u128) + amount_in_with_fee;
        (numerator / denominator) as u64
    }

    public fun take_bps(
        amount: u64,
        fee_rate_bps: u64,
    ): u64 {
        ((amount as u128) * (fee_rate_bps as u128) / (10_000 as u128)) as u64
    }

}