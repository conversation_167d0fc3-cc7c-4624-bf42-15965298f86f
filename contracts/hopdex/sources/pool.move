/*
/// Module: hopdex
module hopdex::hopdex;
*/

// For Move coding conventions, see
// https://docs.sui.io/concepts/sui-move-concepts/conventions


module hopdex::pool {

    use hopdex::config::{DexConfig};
    use hopdex::math;
    use hopdex::events;

    use sui::balance::{Self, Balance};
    use sui::coin::{Self, Coin};
    use sui::sui::{SUI};
    use std::type_name::{Self};

    const E_FEE_OWNER_SENDER_MISMATCH: u64 = 0;
    const E_POSITION_ALREADY_LOCKED: u64 = 1;
    const E_SLIPPAGE: u64 = 2;
    const E_NOT_ENOUGH_COIN: u64 = 3;

    const FEE_GROWTH_SCALER: u128 = 1_000_000_000_000;

    /*
     * Structs
     */

    public struct LiquidityPool<phantom A> has key {
        id: UID,

        balance_a: Balance<A>,
        balance_b: Balance<SUI>,
        fees: Balance<SUI>,

        total_liquidity: u128,
        fee_growth_global: u128,
    }

    public struct Position has key, store {
        id: object::UID,
        pool: object::ID,

        total_liquidity: u128,
        fee_growth_entry: u128,

        locked: bool,
        fee_owner: address, // make this the dev wallet
    }

    /* 
     * Create
     */

    public fun create<A>(
        config: &DexConfig,

        coin_a: Coin<A>,
        coin_b: Coin<SUI>,

        fee_owner: address,
        ctx: &mut TxContext,
    ): ID {
        config.enforce_min_version();
        
        // initial LP is always locked
        let mut pool = LiquidityPool<A> {
            id: object::new(ctx),

            balance_a: balance::zero(),
            balance_b: balance::zero(),
            fees: balance::zero(),

            total_liquidity: 0,
            fee_growth_global: 0
        };
        events::emit_create_pool_event(pool.id.to_inner(), type_name::get<A>(), fee_owner, coin_a.value(), coin_b.value());

        let id = pool.id.to_inner();

        create_position(config, &mut pool, coin_a, coin_b, true, fee_owner, ctx);
        transfer::share_object(pool);

        id
    }

    /*
     * Positions
     */

    public fun create_position<A>(
        config: &DexConfig, 
        pool: &mut LiquidityPool<A>,

        coin_a: Coin<A>,
        coin_b: Coin<SUI>,

        locked: bool,
        fee_owner: address,

        ctx: &mut TxContext,
    ) {
        config.min_version();

        let balance_a = coin_a.into_balance();
        let balance_b = coin_b.into_balance();

        // Ensure the ratio matches the pool's current ratio
        let pool_a = balance::value(&pool.balance_a);
        let pool_b = balance::value(&pool.balance_b);

        // If pool is empty, allow any ratio (should only happen at init)
        if (pool_a > 0 && pool_b > 0) {
            // Cross-multiply to avoid division and rounding errors
            assert!(
                balance_a.value() * pool_b == balance_b.value() * pool_a,
                0 // error code: ratio mismatch
            );
        };

        let amount_a = balance_a.value();
        let amount_b = balance_b.value();

        let total_liquidity = (amount_a as u128) * (amount_b as u128);

        pool.balance_a.join(balance_a);
        pool.balance_b.join(balance_b);
        pool.total_liquidity = pool.total_liquidity + total_liquidity;

        let position = Position {
            id: object::new(ctx),
            pool: object::id(pool),

            total_liquidity,
            fee_growth_entry: pool.fee_growth_global,

            locked,
            fee_owner,
        };

        events::emit_add_liquidity_event(
            pool.id.to_inner(),
            amount_a,
            amount_b,
            pool.balance_a.value(),
            pool.balance_b.value(),
            fee_owner,
        );
        transfer::public_transfer(position, fee_owner);
    }

    public fun claim_fees<A>(
        config: &DexConfig,
        pool: &mut LiquidityPool<A>,
        position: &mut Position,
        ctx: &mut TxContext,
    ) {
        config.enforce_min_version();

        assert!(position.fee_owner == ctx.sender(), E_FEE_OWNER_SENDER_MISMATCH); // error code: not fee owner

        let delta = pool.fee_growth_global - position.fee_growth_entry;
        let owed = ((position.total_liquidity as u128) * delta / FEE_GROWTH_SCALER) as u64;
        position.fee_growth_entry = pool.fee_growth_global;

        let fees = coin::from_balance(pool.fees.split(owed), ctx);
        transfer::public_transfer(fees, position.fee_owner);
    }

    public fun lock_position(
        config: &DexConfig,
        position: &mut Position,
        ctx: &mut TxContext
    ) {
        config.enforce_min_version();

        assert!(!position.locked, E_POSITION_ALREADY_LOCKED);
        assert!(position.fee_owner == ctx.sender(), E_FEE_OWNER_SENDER_MISMATCH);

        position.locked = true;
    }

    public fun close_position<A>(
        config: &DexConfig,
        pool: &mut LiquidityPool<A>,
        mut position: Position,
        ctx: &mut TxContext,
    ) {
        config.enforce_min_version();

        assert!(position.fee_owner == ctx.sender(), E_FEE_OWNER_SENDER_MISMATCH);
        assert!(!position.locked, E_POSITION_ALREADY_LOCKED);

        claim_fees(config, pool, &mut position, ctx);

        let share = position.total_liquidity;
        let total = pool.total_liquidity;

        // Calculate amounts to return
        let amount_a = ((balance::value(&pool.balance_a) as u128) * share / total) as u64;
        let amount_b = ((balance::value(&pool.balance_b) as u128) * share / total) as u64;

        pool.total_liquidity = pool.total_liquidity - share;

        // Return coins to user
        let coin_a = coin::from_balance(pool.balance_a.split(amount_a), ctx);
        let coin_b = coin::from_balance(pool.balance_b.split(amount_b), ctx);

        transfer::public_transfer(coin_a, position.fee_owner);
        transfer::public_transfer(coin_b, position.fee_owner);

        events::emit_remove_liquidity_event(
            pool.id.to_inner(), 
            amount_a,
            amount_b,
            pool.balance_a.value(),
            pool.balance_b.value(),
            position.fee_owner
        );

        let Position { id, pool: _, total_liquidity: _, fee_growth_entry: _, locked: _, fee_owner: _ } = position;
        object::delete(id);
    }

    /*
     * Swaps*/

    public fun swap_a2b<A>(
        config: &DexConfig,
        pool: &mut LiquidityPool<A>,

        mut coin_a: Coin<A>,
        amount_a: u64,

        min_amount_out: u64,
        ctx: &mut TxContext,
    ): (Coin<A>, Coin<SUI>) {
        config.enforce_min_version();
        config.swaps_enabled();

        let amount_out = math::calculate_amount_out(
            amount_a, 
            pool.balance_a.value(), 
            pool.balance_b.value()
        );
        assert!(coin_a.value() >= amount_a, E_NOT_ENOUGH_COIN);
        assert!(amount_out >= min_amount_out, E_SLIPPAGE);

        let coin_in = coin_a.split(amount_a, ctx);
        pool.balance_a.join(coin_in.into_balance());

        let mut coin_b = coin::from_balance(pool.balance_b.split(amount_out), ctx);

        // Calculate fee
        let fee_amount = math::take_bps(amount_out, config.pool_fee_rate_bps());
        let mut fee_coin = coin_b.split(fee_amount, ctx);

        // Protocol fee
        let protocol_fee = math::take_bps(fee_amount, config.protocol_share_bps());
        let protocol_coin = fee_coin.split(protocol_fee, ctx);

        transfer::public_transfer(protocol_coin, config.treasury_address());

        // Update fee growth
        pool.fee_growth_global = pool.fee_growth_global + ((amount_a as u128 * FEE_GROWTH_SCALER / pool.total_liquidity as u128) as u128);
        pool.fees.join(fee_coin.into_balance());

        events::emit_swap_event(pool.id.to_inner(), true, amount_a, coin_b.value(), pool.balance_a.value(), pool.balance_b.value(), ctx.sender());

        // Return output coin
        (coin_a, coin_b)
    }

    public fun swap_b2a<A>(
        config: &DexConfig,
        pool: &mut LiquidityPool<A>,

        mut coin_b: Coin<SUI>,
        amount_b: u64,

        min_amount_out: u64,
        ctx: &mut TxContext,
    ): (Coin<A>, Coin<SUI>) {
        config.enforce_min_version();
        config.swaps_enabled();

        assert!(coin_b.value() >= amount_b, E_NOT_ENOUGH_COIN);

        let fee_amount = math::take_bps(amount_b, config.pool_fee_rate_bps());
        let amount_out = math::calculate_amount_out(
            amount_b - fee_amount,
            pool.balance_b.value(),
            pool.balance_a.value()
        );
        assert!(amount_out >= min_amount_out, E_SLIPPAGE);

        let mut coin_in = coin_b.split(amount_b, ctx);
        let mut fee_coin = coin_in.split(fee_amount, ctx);

        // Protocol fee
        let protocol_fee = math::take_bps(fee_amount, config.protocol_share_bps());
        let protocol_coin = fee_coin.split(protocol_fee, ctx);

        transfer::public_transfer(protocol_coin, config.treasury_address());

        // Update fee growth
        pool.fee_growth_global = pool.fee_growth_global + (((amount_b - fee_amount) as u128 * FEE_GROWTH_SCALER / pool.total_liquidity as u128) as u128);
        pool.fees.join(fee_coin.into_balance());

        pool.balance_b.join(coin_in.into_balance());

        let coin_a = coin::from_balance(pool.balance_a.split(amount_out), ctx);

        events::emit_swap_event(pool.id.to_inner(), false, amount_b, coin_a.value(), pool.balance_a.value(), pool.balance_b.value(), ctx.sender());

        // Return output coin
        (coin_a, coin_b)      
    }

}