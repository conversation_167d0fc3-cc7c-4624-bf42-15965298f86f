module hopfun::math {

    public(package) fun get_fee_amount(amount_in: u64, fee_bps: u64): u64 {
        (((amount_in as u128) * (fee_bps as u128)) / (10_000u128)) as u64
    }

    public(package) fun find_amount_with_fee(for_amount_in: u64, fee_bps: u64): u64 {
        // determines what the input amount has to be including the fee to get the for_amount_in after deducting the fee
        ((for_amount_in * 10_000 as u128) / (10_000 - fee_bps as u128)) as u64
    }

    public(package) fun get_amount_out(amount_in: u64, reserve_in: u64, reserve_out: u64): u64 {
        let numerator: u128 = (amount_in as u128) * (reserve_out as u128);
        let denominator: u128 = (reserve_in as u128) + (amount_in as u128);

        (numerator / denominator) as u64
    }

    public(package) fun get_amount_in(amount_out: u64, reserve_in: u64, reserve_out: u64): u64 {
        let numerator = (reserve_in as u128) * (amount_out as u128);
        let denominator = (reserve_out - amount_out) as u128;

        (numerator / denominator) as u64
    }

}