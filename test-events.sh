#!/bin/bash

echo "🧪 Testing HopFun events on Sui devnet..."

HOPFUN_PACKAGE="0x497ff2769b28664d052601132542a2a77e75339945dc34a494e40fcd680065ac"
RPC_URL="https://fullnode.devnet.sui.io"

echo "📦 HopFun Package ID: $HOPFUN_PACKAGE"
echo "🌐 RPC URL: $RPC_URL"
echo ""

# Test 1: Check ConnectorCreated events
echo "1️⃣ Checking ConnectorCreated events..."
curl -s -X POST "$RPC_URL" \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "id": 1,
    "method": "suix_queryEvents",
    "params": [
      {
        "MoveEventType": "'$HOPFUN_PACKAGE'::events::ConnectorCreated"
      },
      null,
      5,
      true
    ]
  }' | jq '.result.data | length' | xargs -I {} echo "✅ Found {} ConnectorCreated events"

echo ""

# Test 2: Check BondingCurveCreated events  
echo "2️⃣ Checking BondingCurveCreated events..."
curl -s -X POST "$RPC_URL" \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "id": 2,
    "method": "suix_queryEvents",
    "params": [
      {
        "MoveEventType": "'$HOPFUN_PACKAGE'::events::BondingCurveCreated"
      },
      null,
      5,
      true
    ]
  }' | jq '.result.data | length' | xargs -I {} echo "✅ Found {} BondingCurveCreated events"

echo ""

# Test 3: Get latest ConnectorCreated event details
echo "3️⃣ Getting latest ConnectorCreated event details..."
LATEST_CONNECTOR=$(curl -s -X POST "$RPC_URL" \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "id": 3,
    "method": "suix_queryEvents",
    "params": [
      {
        "MoveEventType": "'$HOPFUN_PACKAGE'::events::ConnectorCreated"
      },
      null,
      1,
      true
    ]
  }' | jq -r '.result.data[0]')

if [ "$LATEST_CONNECTOR" != "null" ] && [ "$LATEST_CONNECTOR" != "" ]; then
  echo "📄 Latest ConnectorCreated event:"
  echo "$LATEST_CONNECTOR" | jq '{
    digest: .id.txDigest,
    timestamp: .timestampMs,
    connector_id: .parsedJson.connector_id
  }'
  
  # Get the transaction digest for further analysis
  TX_DIGEST=$(echo "$LATEST_CONNECTOR" | jq -r '.id.txDigest')
  echo ""
  echo "4️⃣ Analyzing transaction: $TX_DIGEST"
  
  # Get transaction details
  curl -s -X POST "$RPC_URL" \
    -H "Content-Type: application/json" \
    -d '{
      "jsonrpc": "2.0",
      "id": 4,
      "method": "sui_getTransactionBlock",
      "params": [
        "'$TX_DIGEST'",
        {
          "showEffects": true,
          "showEvents": true,
          "showObjectChanges": true
        }
      ]
    }' | jq '{
      digest: .result.digest,
      status: .result.effects.status.status,
      gasUsed: .result.effects.gasUsed,
      eventCount: (.result.events | length),
      objectChanges: (.result.objectChanges | length),
      events: [.result.events[] | {type: .type, data: .parsedJson}]
    }'
else
  echo "❌ No ConnectorCreated events found"
fi

echo ""
echo "🏁 Event analysis complete!"
