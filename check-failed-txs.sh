#!/bin/bash

echo "🔍 Checking for failed transactions related to HopFun..."

HOPFUN_PACKAGE="0x497ff2769b28664d052601132542a2a77e75339945dc34a494e40fcd680065ac"
RPC_URL="https://fullnode.devnet.sui.io"

# Get the connector ID from the latest ConnectorCreated event
CONNECTOR_ID=$(curl -s -X POST "$RPC_URL" \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "id": 1,
    "method": "suix_queryEvents",
    "params": [
      {
        "MoveEventType": "'$HOPFUN_PACKAGE'::events::ConnectorCreated"
      },
      null,
      1,
      true
    ]
  }' | jq -r '.result.data[0].parsedJson.connector_id')

echo "🎯 Latest Connector ID: $CONNECTOR_ID"

# Check if this connector still exists (it should be consumed by accept_connector)
echo ""
echo "🔍 Checking if connector object still exists..."
curl -s -X POST "$RPC_URL" \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "id": 2,
    "method": "sui_getObject",
    "params": [
      "'$CONNECTOR_ID'",
      {
        "showContent": true,
        "showOwner": true,
        "showType": true
      }
    ]
  }' | jq '{
    exists: (.result.data != null),
    owner: .result.data.owner,
    type: .result.data.type,
    status: .result.data.content.fields
  }'

echo ""
echo "💡 If the connector still exists, it means accept_connector was never called successfully."
echo "💡 If the connector doesn't exist, it was consumed but no BondingCurveCreated event was emitted."

# Check for any transactions involving this connector
echo ""
echo "🔍 Checking for transactions involving this connector..."
curl -s -X POST "$RPC_URL" \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "id": 3,
    "method": "suix_queryTransactionBlocks",
    "params": [
      {
        "filter": {
          "InputObject": "'$CONNECTOR_ID'"
        }
      },
      null,
      5,
      true
    ]
  }' | jq '.result.data | length' | xargs -I {} echo "📊 Found {} transactions involving this connector"
