// Test manual event processing for BondingCurveCreated event
import { SuiClient } from '@mysten/sui/client';

const client = new SuiClient({
  url: 'https://fullnode.devnet.sui.io',
});

// Known transaction with BondingCurveCreated event
const TX_DIGEST = '8h92uSdBYsarWqdZej2aDMpxFrxLh3hg6UgsxFHG16rY';

async function testManualEventProcessing() {
  console.log('🧪 Testing manual event processing...');
  
  try {
    // Get the transaction with events
    console.log('1️⃣ Fetching transaction with events...');
    const txResult = await client.getTransactionBlock({
      digest: TX_DIGEST,
      options: {
        showEvents: true,
        showEffects: true,
        showObjectChanges: true,
      },
    });
    
    console.log('✅ Transaction fetched successfully');
    console.log('📊 Events found:', txResult.events?.length || 0);
    
    if (!txResult.events || txResult.events.length === 0) {
      console.log('❌ No events found in transaction');
      return;
    }
    
    // Process each event
    for (const [index, event] of txResult.events.entries()) {
      console.log(`\n2️⃣ Processing event ${index + 1}/${txResult.events.length}:`);
      console.log('📋 Event details:', {
        type: event.type,
        packageId: event.packageId,
        sender: event.sender,
        timestampMs: event.timestampMs,
      });
      
      // Test the getEventType function logic
      const eventTypes = {
        ConnectorCreated: 'ConnectorCreated',
        BondingCurveCreated: 'BondingCurveCreated',
        BondingCurveBuy: 'BondingCurveBuy',
        BondingCurveSell: 'BondingCurveSell',
        BondingCurveComplete: 'BondingCurveComplete',
        BondingCurveMigrate: 'BondingCurveMigrate',
        MemeConfigUpdated: 'MemeConfigUpdated',
      };
      
      // Handle generic type parameters
      const cleanEventType = event.type.split('<')[0];
      const eventName = cleanEventType.split('::').pop();
      
      console.log('🔍 Event type analysis:', {
        originalType: event.type,
        cleanType: cleanEventType,
        eventName: eventName,
        hasMapping: eventName ? eventTypes.hasOwnProperty(eventName) : false,
        mappedType: eventName && eventTypes.hasOwnProperty(eventName) ? eventTypes[eventName] : null,
      });
      
      if (eventName && eventTypes.hasOwnProperty(eventName)) {
        const mappedEventType = eventTypes[eventName];
        console.log(`✅ Event would be processed as: ${mappedEventType}`);
        
        if (mappedEventType === 'BondingCurveCreated') {
          console.log('🎉 Found BondingCurveCreated event!');
          console.log('📄 Event data:', JSON.stringify(event.parsedJson, null, 2));
          
          // Test if this would be processed by our indexer
          const hopfunPackageId = '0x497ff2769b28664d052601132542a2a77e75339945dc34a494e40fcd680065ac';
          const isHopfunEvent = 
            event.packageId === hopfunPackageId &&
            event.type.includes(`${hopfunPackageId}::events::`);
          
          console.log('🔍 HopFun event check:', {
            packageId: event.packageId,
            expectedPackageId: hopfunPackageId,
            typeIncludes: event.type.includes(`${hopfunPackageId}::events::`),
            isHopfunEvent: isHopfunEvent,
          });
          
          if (isHopfunEvent) {
            console.log('✅ This event SHOULD be processed by the indexer');
          } else {
            console.log('❌ This event would be filtered out by the indexer');
          }
        }
      } else {
        console.log(`❌ Event would be ignored: ${eventName || 'unknown'}`);
      }
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

testManualEventProcessing();
