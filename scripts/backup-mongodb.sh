#!/bin/bash

# MongoDB Backup Script
# This script creates compressed backups of MongoDB databases

set -e

# Configuration from environment variables
MONGO_HOST="${MONGO_HOST:-mongodb-primary}"
MONGO_PORT="${MONGO_PORT:-27017}"
MONGO_USERNAME="${MONGO_ROOT_USERNAME:-admin}"
MONGO_PASSWORD="${MONGO_ROOT_PASSWORD:-password}"
MONGO_DATABASE="${MONGO_DATABASE:-hopfun}"
BACKUP_DIR="${BACKUP_DIR:-/backups}"
BACKUP_RETENTION_DAYS="${BACKUP_RETENTION_DAYS:-7}"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Functions
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Create backup directory if it doesn't exist
mkdir -p "${BACKUP_DIR}"

# Backup filename
BACKUP_NAME="${MONGO_DATABASE}_${TIMESTAMP}"
BACKUP_PATH="${BACKUP_DIR}/${BACKUP_NAME}"

log_info "Starting MongoDB backup..."
log_info "Database: ${MONGO_DATABASE}"
log_info "Backup path: ${BACKUP_PATH}"

# Perform the backup
if mongodump \
    --host="${MONGO_HOST}:${MONGO_PORT}" \
    --username="${MONGO_USERNAME}" \
    --password="${MONGO_PASSWORD}" \
    --authenticationDatabase=admin \
    --db="${MONGO_DATABASE}" \
    --out="${BACKUP_PATH}" \
    --numParallelCollections=4 \
    --oplog; then
    
    log_info "Backup completed successfully"
    
    # Compress the backup
    log_info "Compressing backup..."
    tar -czf "${BACKUP_PATH}.tar.gz" -C "${BACKUP_DIR}" "${BACKUP_NAME}"
    
    # Remove uncompressed backup
    rm -rf "${BACKUP_PATH}"
    
    # Calculate backup size
    BACKUP_SIZE=$(du -h "${BACKUP_PATH}.tar.gz" | cut -f1)
    log_info "Backup compressed successfully. Size: ${BACKUP_SIZE}"
    
    # Create backup metadata
    cat > "${BACKUP_PATH}.meta.json" <<EOF
{
    "timestamp": "${TIMESTAMP}",
    "database": "${MONGO_DATABASE}",
    "host": "${MONGO_HOST}",
    "size": "${BACKUP_SIZE}",
    "retention_days": ${BACKUP_RETENTION_DAYS},
    "created_at": "$(date -u +%Y-%m-%dT%H:%M:%SZ)"
}
EOF
    
    # Clean up old backups
    log_info "Cleaning up old backups (older than ${BACKUP_RETENTION_DAYS} days)..."
    find "${BACKUP_DIR}" -name "${MONGO_DATABASE}_*.tar.gz" -type f -mtime +${BACKUP_RETENTION_DAYS} -delete
    find "${BACKUP_DIR}" -name "${MONGO_DATABASE}_*.meta.json" -type f -mtime +${BACKUP_RETENTION_DAYS} -delete
    
    # List current backups
    log_info "Current backups:"
    ls -lh "${BACKUP_DIR}"/${MONGO_DATABASE}_*.tar.gz 2>/dev/null || log_warning "No backups found"
    
    # Upload to S3 if configured
    if [ ! -z "${AWS_ACCESS_KEY_ID}" ] && [ ! -z "${S3_BUCKET}" ]; then
        log_info "Uploading backup to S3..."
        if aws s3 cp "${BACKUP_PATH}.tar.gz" "s3://${S3_BUCKET}/mongodb-backups/${BACKUP_NAME}.tar.gz"; then
            log_info "Backup uploaded to S3 successfully"
        else
            log_error "Failed to upload backup to S3"
        fi
    fi
    
    log_info "Backup process completed successfully!"
    exit 0
else
    log_error "Backup failed!"
    exit 1
fi