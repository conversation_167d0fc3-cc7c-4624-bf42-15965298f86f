#!/bin/bash

# HopFun Indexer Testing Script
# This script tests if the indexer is working properly

set -e

echo "🧪 HopFun Indexer Testing Script"
echo "================================="
echo

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    local status=$1
    local message=$2
    case $status in
        "PASS") echo -e "${GREEN}✅ PASS${NC}: $message" ;;
        "FAIL") echo -e "${RED}❌ FAIL${NC}: $message" ;;
        "WARN") echo -e "${YELLOW}⚠️  WARN${NC}: $message" ;;
        "INFO") echo -e "${BLUE}ℹ️  INFO${NC}: $message" ;;
    esac
}

# Test 1: Check if MongoDB is running
echo "1. Testing MongoDB Container..."
if docker-compose ps mongodb | grep -q "Up"; then
    print_status "PASS" "MongoDB container is running"
else
    print_status "FAIL" "MongoDB container is not running"
    echo "   Run: docker-compose up -d"
    echo "   Cannot proceed with MongoDB tests"
    MONGODB_FAILED=true
fi

# Test 2: Check MongoDB connectivity
echo
echo "2. Testing MongoDB Connectivity..."
if [ "${MONGODB_FAILED:-false}" = "true" ]; then
    print_status "SKIP" "Skipping MongoDB connectivity test (container not running)"
else
    MONGO_TEST=$(docker-compose exec -T mongodb mongosh --username admin --password password --authenticationDatabase admin --quiet --eval "db.adminCommand('ping')" 2>/dev/null)
    if echo "$MONGO_TEST" | grep -q "ok: 1"; then
        print_status "PASS" "MongoDB is accessible and authenticated"
    else
        print_status "FAIL" "Cannot connect to MongoDB"
        echo "   Debug output: $MONGO_TEST"
        echo "   This will prevent the indexer from starting properly"
        MONGODB_FAILED=true
    fi
fi

# Test 3: Check replica set status
echo
echo "3. Testing MongoDB Replica Set..."
if [ "${MONGODB_FAILED:-false}" = "true" ]; then
    print_status "SKIP" "Skipping replica set test (MongoDB not accessible)"
else
    RS_STATUS=$(docker-compose exec -T mongodb mongosh --username admin --password password --authenticationDatabase admin --quiet --eval "try { rs.status().myState } catch(e) { 0 }" 2>/dev/null)
    # Clean the output to get just the number
    RS_STATE=$(echo "$RS_STATUS" | grep -o "[0-9]" | head -1)
    if [ "$RS_STATE" = "1" ]; then
        print_status "PASS" "MongoDB replica set is PRIMARY"
    elif [ "$RS_STATE" = "2" ]; then
        print_status "WARN" "MongoDB replica set is SECONDARY (might work)"
    else
        print_status "WARN" "MongoDB replica set status unclear (might still work)"
        print_status "INFO" "Replica set status: $RS_STATUS"
    fi
fi

# Test 4: Check Sui blockchain connectivity
echo
echo "4. Testing Sui Blockchain Connectivity..."
SUI_RESPONSE=$(curl -s -X POST https://fullnode.devnet.sui.io -H "Content-Type: application/json" -d '{"jsonrpc":"2.0","id":1,"method":"sui_getChainIdentifier","params":[]}')
if echo "$SUI_RESPONSE" | grep -q '"result"'; then
    print_status "PASS" "Sui blockchain is accessible"
else
    print_status "FAIL" "Cannot connect to Sui blockchain"
fi

# Test 5: Check if indexer HTTP server is running
echo
echo "5. Testing Indexer HTTP Server..."
if curl -s -f http://localhost:3001/status > /dev/null 2>&1; then
    print_status "PASS" "Indexer HTTP server is running"
    
    # Get status details
    STATUS=$(curl -s http://localhost:3001/status | jq -r '.status // "unknown"' 2>/dev/null || echo "unknown")
    print_status "INFO" "Indexer status: $STATUS"
    
    # Test health endpoint
    HEALTH=$(curl -s http://localhost:3001/health | jq -r '.status // "unknown"' 2>/dev/null || echo "unknown")
    if [ "$HEALTH" = "healthy" ]; then
        print_status "PASS" "Indexer health check: $HEALTH"
    else
        print_status "WARN" "Indexer health check: $HEALTH"
    fi
else
    print_status "FAIL" "Indexer HTTP server is not running"
    print_status "INFO" "Start with: pnpm --filter indexer dev"
fi

# Test 6: Check database records
echo
echo "6. Testing Database Records..."

# Check IndexerState
INDEXER_STATE_COUNT=$(docker-compose exec -T mongodb mongosh --username admin --password password --authenticationDatabase admin hopfun --quiet --eval "db.indexer_state.countDocuments()" 2>/dev/null || echo "0")
if [ "$INDEXER_STATE_COUNT" -gt 0 ]; then
    print_status "PASS" "IndexerState records found: $INDEXER_STATE_COUNT"
else
    print_status "WARN" "No IndexerState records found (indexer may not have started successfully)"
fi

# Check for any event records
EVENT_COUNT=$(docker-compose exec -T mongodb mongosh --username admin --password password --authenticationDatabase admin hopfun --quiet --eval "db.bonding_curve_created_events.countDocuments() + db.bonding_curve_transactions.countDocuments() + db.connector_created_events.countDocuments()" 2>/dev/null || echo "0")
if [ "$EVENT_COUNT" -gt 0 ]; then
    print_status "PASS" "Event records found: $EVENT_COUNT"
else
    print_status "INFO" "No event records found (normal if no blockchain activity)"
fi

# Check for tokens
TOKEN_COUNT=$(docker-compose exec -T mongodb mongosh --username admin --password password --authenticationDatabase admin hopfun --quiet --eval "db.tokens.countDocuments()" 2>/dev/null || echo "0")
if [ "$TOKEN_COUNT" -gt 0 ]; then
    print_status "PASS" "Token records found: $TOKEN_COUNT"
else
    print_status "INFO" "No token records found (normal if no tokens created)"
fi

echo
echo "📊 Summary of Collections:"
echo "=========================="
docker-compose exec -T mongodb mongosh --username admin --password password --authenticationDatabase admin hopfun --quiet --eval "
db.getCollectionNames().forEach(function(collection) {
    var count = db.getCollection(collection).countDocuments();
    print(collection + ': ' + count + ' documents');
});
" 2>/dev/null || echo "Could not retrieve collection counts"

echo
echo "🎯 Quick Test Commands:"
echo "======================"
echo "Check indexer status:     curl http://localhost:3001/status"
echo "Check indexer health:     curl http://localhost:3001/health"
echo "Check indexer metrics:    curl http://localhost:3001/metrics"
echo "View MongoDB collections: docker-compose exec mongodb mongosh --username admin --password password --authenticationDatabase admin hopfun"
echo "Start indexer:            pnpm --filter indexer dev"
echo "View indexer logs:        Check the terminal where you ran 'pnpm --filter indexer dev'"

echo
echo "🔍 For detailed debugging:"
echo "=========================="
echo "1. Check indexer logs for errors"
echo "2. Verify DATABASE_URL in apps/indexer/.env"
echo "3. Ensure MongoDB replica set is working: docker-compose logs mongodb-replica-setup"
echo "4. Test network connectivity between indexer and MongoDB"

echo
echo "🎯 Test Results Summary:"
echo "======================="
if [ "${MONGODB_FAILED:-false}" = "true" ]; then
    print_status "FAIL" "MongoDB connectivity issues detected"
    echo "   Fix MongoDB before running the indexer:"
    echo "   1. Check: docker-compose ps mongodb"
    echo "   2. Check: docker-compose logs mongodb"
    echo "   3. Restart: docker-compose restart mongodb"
else
    print_status "PASS" "Core infrastructure tests passed"
fi

print_status "INFO" "Testing complete!"