#!/bin/bash

# MongoDB Replica Set Initialization Script
# Supports both single-node (development) and multi-node (production) setups

set -e

# Configuration
MONGO_HOST="${MONGO_HOST:-mongodb-primary}"
MONGO_USERNAME="${MONGO_ROOT_USERNAME:-admin}"
MONGO_PASSWORD="${MONGO_ROOT_PASSWORD:-password}"
REPLICA_SET="${MONGO_REPLICA_SET_NAME:-rs0}"
PRODUCTION_MODE="${PRODUCTION_MODE:-false}"

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Wait for MongoDB to be ready
log_info "Waiting for MongoDB to be ready..."
until mongosh --host ${MONGO_HOST}:27017 \
    --username ${MONGO_USERNAME} \
    --password ${MONGO_PASSWORD} \
    --authenticationDatabase admin \
    --eval "print('MongoDB is ready')" \
    --quiet; do
  sleep 2
done

# Check if replica set is already initialized
log_info "Checking replica set status..."
RS_STATUS=$(mongosh --host ${MONGO_HOST}:27017 \
    --username ${MONGO_USERNAME} \
    --password ${MONGO_PASSWORD} \
    --authenticationDatabase admin \
    --eval "rs.status().ok" \
    --quiet 2>/dev/null || echo "0")

if [ "$RS_STATUS" = "1" ]; then
    log_warning "Replica set is already initialized. Checking configuration..."
    
    # Get current replica set configuration
    mongosh --host ${MONGO_HOST}:27017 \
        --username ${MONGO_USERNAME} \
        --password ${MONGO_PASSWORD} \
        --authenticationDatabase admin \
        --eval "printjson(rs.conf())" \
        --quiet
    
    log_info "Replica set already configured. Exiting."
    exit 0
fi

# Initialize replica set based on environment
if [ "$PRODUCTION_MODE" = "true" ]; then
    log_info "Initializing production replica set with multiple nodes..."
    
    # Production configuration with multiple nodes
    mongosh --host ${MONGO_HOST}:27017 \
        --username ${MONGO_USERNAME} \
        --password ${MONGO_PASSWORD} \
        --authenticationDatabase admin \
        --eval "
        rs.initiate({
            _id: '${REPLICA_SET}',
            members: [
                {
                    _id: 0,
                    host: 'mongodb-primary:27017',
                    priority: 2
                },
                {
                    _id: 1,
                    host: 'mongodb-secondary1:27017',
                    priority: 1
                },
                {
                    _id: 2,
                    host: 'mongodb-secondary2:27017',
                    priority: 1
                },
                {
                    _id: 3,
                    host: 'mongodb-arbiter:27017',
                    arbiterOnly: true
                }
            ],
            settings: {
                chainingAllowed: true,
                heartbeatIntervalMillis: 2000,
                heartbeatTimeoutSecs: 10,
                electionTimeoutMillis: 10000,
                catchUpTimeoutMillis: 60000,
                catchUpTakeoverDelayMillis: 30000,
                getLastErrorDefaults: {
                    w: 'majority',
                    wtimeout: 5000
                }
            }
        })
    "
else
    log_info "Initializing development replica set with single node..."
    
    # Development configuration with single node
    mongosh --host ${MONGO_HOST}:27017 \
        --username ${MONGO_USERNAME} \
        --password ${MONGO_PASSWORD} \
        --authenticationDatabase admin \
        --eval "
        rs.initiate({
            _id: '${REPLICA_SET}',
            members: [
                {
                    _id: 0,
                    host: '${MONGO_HOST}:27017'
                }
            ]
        })
    "
fi

# Wait for replica set to be ready
log_info "Waiting for replica set to be ready..."
sleep 5

# Wait for primary to be elected
until mongosh --host ${MONGO_HOST}:27017 \
    --username ${MONGO_USERNAME} \
    --password ${MONGO_PASSWORD} \
    --authenticationDatabase admin \
    --eval "rs.isMaster().ismaster" \
    --quiet | grep -q "true"; do
  log_info "Waiting for primary node election..."
  sleep 2
done

# Display replica set status
log_info "Replica set initialization completed!"
mongosh --host ${MONGO_HOST}:27017 \
    --username ${MONGO_USERNAME} \
    --password ${MONGO_PASSWORD} \
    --authenticationDatabase admin \
    --eval "printjson(rs.status())" \
    --quiet

# Run additional user creation script if exists
if [ -f "/scripts/mongodb-users.js" ]; then
    log_info "Creating additional database users..."
    mongosh --host ${MONGO_HOST}:27017 \
        --username ${MONGO_USERNAME} \
        --password ${MONGO_PASSWORD} \
        --authenticationDatabase admin \
        /scripts/mongodb-users.js
fi

log_info "MongoDB replica set setup completed successfully!"