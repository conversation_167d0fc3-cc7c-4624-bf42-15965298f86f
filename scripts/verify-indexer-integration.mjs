#!/usr/bin/env node

/**
 * This script verifies the complete indexer integration:
 * 1. Checks current database state
 * 2. Monitors for new events
 * 3. Verifies that new tokens appear in the database
 * 4. Tests the server API to retrieve indexed tokens
 */

import { spawn } from 'child_process';
import { createReadStream } from 'fs';
import { join } from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const GREEN = '\x1b[32m';
const RED = '\x1b[31m';
const YELLOW = '\x1b[33m';
const NC = '\x1b[0m';

console.log(`${GREEN}=====================================`);
console.log('Verifying Indexer Integration');
console.log(`=====================================${NC}\n`);

async function checkDatabaseState() {
  console.log(`${YELLOW}1. Checking current database state...${NC}`);
  
  return new Promise((resolve) => {
    const checkDb = spawn('node', ['-e', `
      const { PrismaClient } = require('@hopfun/database');
      const prisma = new PrismaClient();
      
      async function check() {
        try {
          const tokenCount = await prisma.token.count();
          const txCount = await prisma.bondingCurveTransaction.count();
          const indexerState = await prisma.indexerState.findFirst();
          
          console.log('Tokens:', tokenCount);
          console.log('Transactions:', txCount);
          console.log('Last checkpoint:', indexerState?.lastProcessedCheckpoint || 'Not set');
          
          await prisma.$disconnect();
          process.exit(0);
        } catch (error) {
          console.error('Database error:', error.message);
          process.exit(1);
        }
      }
      
      check();
    `], {
      cwd: join(__dirname, '../apps/indexer'),
      env: { ...process.env, DATABASE_URL: '*******************************************************************************' }
    });
    
    checkDb.stdout.on('data', (data) => {
      console.log(`   ${data.toString().trim()}`);
    });
    
    checkDb.stderr.on('data', (data) => {
      console.error(`   ${RED}${data.toString().trim()}${NC}`);
    });
    
    checkDb.on('close', (code) => {
      if (code === 0) {
        console.log(`${GREEN}✅ Database check complete${NC}`);
      } else {
        console.log(`${RED}❌ Database check failed${NC}`);
      }
      resolve();
    });
  });
}

async function testServerAPI() {
  console.log(`\n${YELLOW}2. Testing Server API endpoints...${NC}`);
  
  try {
    // Test the token list endpoint
    const response = await fetch('http://localhost:3000/api/token?limit=5');
    
    if (response.ok) {
      const data = await response.json();
      console.log(`${GREEN}✅ API is responding${NC}`);
      console.log(`   Found ${data.data?.tokens?.length || 0} tokens`);
      
      if (data.data?.tokens?.length > 0) {
        console.log('\n   Sample tokens:');
        data.data.tokens.slice(0, 3).forEach(token => {
          console.log(`   - ${token.coinName} (${token.ticker})`);
          console.log(`     Market Cap: ${token.marketCap}`);
          console.log(`     Status: ${token.status}`);
        });
      }
    } else {
      console.log(`${YELLOW}⚠️  Server API not responding (status: ${response.status})${NC}`);
      console.log('   Make sure the server is running: cd apps/server && pnpm dev');
    }
  } catch (error) {
    console.log(`${YELLOW}⚠️  Could not connect to server API${NC}`);
    console.log(`   Error: ${error.message}`);
    console.log('   Make sure the server is running: cd apps/server && pnpm dev');
  }
}

async function checkIndexerLogs() {
  console.log(`\n${YELLOW}3. Checking indexer logs...${NC}`);
  
  const logPath = join(__dirname, '../apps/indexer/indexer_output.log');
  
  try {
    // Try to read recent log output
    const { promises: fs } = await import('fs');
    const logContent = await fs.readFile(logPath, 'utf-8').catch(() => null);
    
    if (logContent) {
      const lines = logContent.split('\n').filter(line => line.trim());
      const recentLines = lines.slice(-10);
      
      console.log('   Recent indexer activity:');
      recentLines.forEach(line => {
        if (line.includes('Error') || line.includes('Failed')) {
          console.log(`   ${RED}${line}${NC}`);
        } else if (line.includes('Processing') || line.includes('Indexed')) {
          console.log(`   ${GREEN}${line}${NC}`);
        } else {
          console.log(`   ${line}`);
        }
      });
    } else {
      console.log('   No indexer logs found yet');
    }
  } catch (error) {
    console.log('   Could not read indexer logs');
  }
}

async function main() {
  // Check database state
  await checkDatabaseState();
  
  // Test server API
  await testServerAPI();
  
  // Check indexer logs
  await checkIndexerLogs();
  
  console.log(`\n${GREEN}=====================================`);
  console.log('Integration Check Complete');
  console.log(`=====================================${NC}`);
  
  console.log('\n📝 Next Steps:');
  console.log('1. If the indexer is not running:');
  console.log('   cd apps/indexer && pnpm start');
  console.log('\n2. If the server is not running:');
  console.log('   cd apps/server && pnpm dev');
  console.log('\n3. To create a new token:');
  console.log('   - Open the frontend (cd apps/frontend && pnpm dev)');
  console.log('   - Navigate to token creation page');
  console.log('   - Create a new token');
  console.log('   - The indexer should automatically process it');
  console.log('\n4. To monitor indexer activity:');
  console.log('   tail -f apps/indexer/indexer_output.log');
}

main().catch(console.error);