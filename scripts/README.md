# Scripts Directory

Essential scripts for HopFun deployment and maintenance.

## Quick Reference

### 🚀 Deployment Scripts

#### `deploy-all-contracts.sh`
Deploys all smart contracts to Sui blockchain in the correct order.
```bash
bash scripts/deploy-all-contracts.sh
```

#### `check-deployment.sh`
Verifies deployment status and shows all contract addresses.
```bash
bash scripts/check-deployment.sh
```

#### `extract-bytecode.js`
Generates bytecode for frontend token creation.
```bash
node scripts/extract-bytecode.js
```

### 🗄️ Database Scripts

#### `mongo-init.js`
MongoDB initialization script for collections and indexes.

#### `mongodb-keyfile`
Authentication key for MongoDB replica set.

#### `rs-init.sh`
Initializes MongoDB replica set configuration.

### 🧪 Testing Scripts

#### `test-indexer.sh`
Tests the blockchain event indexer functionality.
```bash
bash scripts/test-indexer.sh
```

## Typical Workflow

1. **Deploy contracts:**
   ```bash
   bash scripts/deploy-all-contracts.sh
   ```

2. **Verify deployment:**
   ```bash
   bash scripts/check-deployment.sh
   ```

3. **Generate bytecode for frontend:**
   ```bash
   node scripts/extract-bytecode.js
   ```

## For Full Documentation

See [DEPLOYMENT.md](../DEPLOYMENT.md) for comprehensive deployment instructions.