#!/usr/bin/env node

import { PrismaClient } from '../packages/database/dist/index.js';
import { createSuiClient } from '@mysten/sui/client';
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import fs from 'fs';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load indexer environment variables
const indexerEnvPath = join(__dirname, '../apps/indexer/.env');
if (fs.existsSync(indexerEnvPath)) {
  dotenv.config({ path: indexerEnvPath });
}

console.log('=====================================');
console.log('Checking Indexer Setup and Connectivity');
console.log('=====================================\n');

// 1. Check environment variables
console.log('1. Environment Configuration:');
const requiredVars = [
  'DATABASE_URL',
  'SUI_RPC_URL',
  'HOPFUN_PACKAGE_ID',
  'HOPDEX_PACKAGE_ID',
  'NETWORK'
];

let allVarsPresent = true;
for (const varName of requiredVars) {
  const value = process.env[varName];
  if (value) {
    console.log(`✅ ${varName}: ${value.substring(0, 50)}...`);
  } else {
    console.log(`❌ ${varName}: NOT SET`);
    allVarsPresent = false;
  }
}

if (!allVarsPresent) {
  console.error('\n❌ Missing required environment variables. Check apps/indexer/.env');
  process.exit(1);
}

// 2. Test Database Connection
console.log('\n2. Testing Database Connection:');
const prisma = new PrismaClient();

try {
  // Test connection
  await prisma.$connect();
  console.log('✅ Successfully connected to MongoDB');
  
  // Check database state
  const [tokenCount, indexerState, eventCount] = await Promise.all([
    prisma.token.count(),
    prisma.indexerState.findFirst(),
    prisma.connectorCreatedEvent.count()
  ]);
  
  console.log(`   - Tokens in database: ${tokenCount}`);
  console.log(`   - Connector events: ${eventCount}`);
  console.log(`   - Indexer state: ${indexerState ? `Checkpoint ${indexerState.lastProcessedCheckpoint}` : 'Not initialized'}`);
  
  // Check for recent tokens
  const recentTokens = await prisma.token.findMany({
    take: 3,
    orderBy: { createdAt: 'desc' },
    include: { stats: true }
  });
  
  if (recentTokens.length > 0) {
    console.log('\n   Recent tokens:');
    for (const token of recentTokens) {
      console.log(`   - ${token.coinName} (${token.ticker})`);
      console.log(`     Market Cap: ${token.marketCap}`);
      console.log(`     Created: ${token.createdAt}`);
      if (token.stats) {
        console.log(`     Volume 24h: ${token.stats.volume24h}`);
        console.log(`     Transactions 24h: ${token.stats.transactions24h}`);
      }
    }
  }
  
} catch (error) {
  console.error(`❌ Database connection failed: ${error.message}`);
  process.exit(1);
} finally {
  await prisma.$disconnect();
}

// 3. Test Sui RPC Connection
console.log('\n3. Testing Sui RPC Connection:');
try {
  const client = createSuiClient({
    url: process.env.SUI_RPC_URL
  });
  
  const latestCheckpoint = await client.getLatestCheckpointSequenceNumber();
  console.log(`✅ Successfully connected to Sui RPC`);
  console.log(`   Latest checkpoint: ${latestCheckpoint}`);
  
  // Check if our contracts are deployed
  console.log('\n4. Verifying Deployed Contracts:');
  
  const packages = [
    { name: 'HopFun', id: process.env.HOPFUN_PACKAGE_ID },
    { name: 'HopDex', id: process.env.HOPDEX_PACKAGE_ID }
  ];
  
  for (const pkg of packages) {
    try {
      const packageObj = await client.getObject({
        id: pkg.id,
        options: { showType: true }
      });
      
      if (packageObj.data) {
        console.log(`✅ ${pkg.name} package found: ${pkg.id}`);
        console.log(`   Type: ${packageObj.data.type || 'Package'}`);
      } else {
        console.log(`❌ ${pkg.name} package not found: ${pkg.id}`);
      }
    } catch (error) {
      console.log(`❌ Error checking ${pkg.name} package: ${error.message}`);
    }
  }
  
  // Check for recent events from our packages
  console.log('\n5. Checking for Recent Events:');
  try {
    const events = await client.queryEvents({
      query: { 
        MoveEventModule: { 
          package: process.env.HOPFUN_PACKAGE_ID,
          module: 'connector'
        } 
      },
      limit: 5,
      order: 'descending'
    });
    
    console.log(`   Found ${events.data.length} recent connector events`);
    if (events.data.length > 0) {
      console.log('   Recent event types:');
      for (const event of events.data) {
        console.log(`   - ${event.type} at checkpoint ${event.checkpoint}`);
      }
    }
  } catch (error) {
    console.log(`   No recent events found or error querying: ${error.message}`);
  }
  
} catch (error) {
  console.error(`❌ Sui RPC connection failed: ${error.message}`);
  process.exit(1);
}

// 6. Check indexer processor files
console.log('\n6. Verifying Indexer Processors:');
const processorDir = join(__dirname, '../apps/indexer/src/processors');
const processors = [
  'connector-created.ts',
  'bonding-curve-created.ts',
  'bonding-curve-buy.ts',
  'bonding-curve-sell.ts',
  'bonding-curve-complete.ts',
  'bonding-curve-migrate.ts',
  'engine.ts'
];

let allProcessorsPresent = true;
for (const processor of processors) {
  const processorPath = join(processorDir, processor);
  if (fs.existsSync(processorPath)) {
    console.log(`✅ ${processor}`);
  } else {
    console.log(`❌ ${processor} - NOT FOUND`);
    allProcessorsPresent = false;
  }
}

if (!allProcessorsPresent) {
  console.error('\n❌ Some processor files are missing');
}

console.log('\n=====================================');
console.log('Indexer Setup Check Complete');
console.log('=====================================');

if (tokenCount === 0) {
  console.log('\n📝 Note: No tokens found in database yet.');
  console.log('This is normal if no tokens have been created.');
  console.log('To start indexing:');
  console.log('  1. cd apps/indexer');
  console.log('  2. pnpm start');
  console.log('  3. Create a token through the frontend');
} else {
  console.log('\n✅ Indexer appears to be configured correctly!');
  console.log('Tokens are already being indexed.');
}

process.exit(0);