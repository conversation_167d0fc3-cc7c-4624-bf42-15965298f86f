#!/usr/bin/env node

import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🔨 Building coin_template with bytecode dump...');

// Build the coin template with registry support
const buildOutput = execSync(
  'sui move build --dump-bytecode-as-base64 --path contracts/coin_template',
  { encoding: 'utf8' },
);
console.log(buildOutput);

// Path to the build output
const buildPath = path.join(
  __dirname,
  '..',
  'contracts',
  'coin_template',
  'build',
  'coin_template',
);
const bytecodePath = path.join(buildPath, 'bytecode_modules');

// Find the template bytecode file
const files = fs.readdirSync(bytecodePath);
const templateFile = files.find((f) => f.includes('template'));

if (!templateFile) {
  console.error('❌ Could not find template bytecode file');
  process.exit(1);
}

// Read the bytecode
const bytecodeBinary = fs.readFileSync(path.join(bytecodePath, templateFile));
const bytecodeBase64 = bytecodeBinary.toString('base64');

// Read deployed package IDs from config
const deploymentsPath = path.join(
  __dirname,
  '..',
  'config',
  'deployments.json',
);
const deployments = JSON.parse(fs.readFileSync(deploymentsPath, 'utf8'));

// Get the deployed package IDs
const hopfunPackageId = deployments.deployments.devnet.hopfun.packageId;
const registryPackageId = deployments.deployments.devnet.registry.packageId;
const hopdexPackageId = deployments.deployments.devnet.hopdex.packageId;

if (!hopfunPackageId || !registryPackageId || !hopdexPackageId) {
  console.error('❌ Missing package IDs in deployments.json');
  console.error(`   HopFun: ${hopfunPackageId}`);
  console.error(`   Registry: ${registryPackageId}`);
  console.error(`   HopDex: ${hopdexPackageId}`);
  process.exit(1);
}

// Build the dependencies array with actual deployed package IDs
// Order matters: Sui stdlib, Sui framework, then our packages in dependency order
// The coin template depends on: config_registry, hopdex (via hopfun), hopfun
const dependencies = [
  '0x0000000000000000000000000000000000000000000000000000000000000001', // Sui stdlib
  '0x0000000000000000000000000000000000000000000000000000000000000002', // Sui framework
  registryPackageId, // config_registry
  hopdexPackageId, // hopdex (needed by hopfun)
  hopfunPackageId, // hopfun
];

console.log('📦 Found bytecode and dependencies');
console.log('📋 Dependencies configured:');
console.log(`   1. Sui stdlib: 0x1`);
console.log(`   2. Sui framework: 0x2`);
console.log(`   3. Config Registry: ${registryPackageId}`);
console.log(`   4. HopDex: ${hopdexPackageId}`);
console.log(`   5. HopFun: ${hopfunPackageId}`);

// Create the bytecode data object
const bytecodeData = {
  bytecode: bytecodeBase64,
  dependencies: dependencies,
  metadata: {
    name: 'TEMPLATE',
    symbol: 'TEMPLATE',
    description: 'Template coin with registry support',
    iconUrl: 'ImageUrl',
    totalSupply: '1000000000000000',
    decimals: 6,
    usesRegistry: true,
    version: '2.0.0',
  },
  checksum: bytecodeBase64.slice(-2),
  generatedAt: new Date().toISOString(),
};

// Write to the frontend service directory
const outputPath = path.join(
  __dirname,
  '..',
  'apps',
  'frontend',
  'src',
  'services',
  'bytecode-data.json',
);
fs.writeFileSync(outputPath, JSON.stringify(bytecodeData, null, 2));

console.log('✅ Bytecode extracted successfully!');
console.log(`📦 Bytecode size: ${bytecodeBinary.length} bytes`);
console.log(`📝 Output saved to: ${outputPath}`);
console.log(`🔐 Checksum: ${bytecodeData.checksum}`);
console.log('🎯 Using Config Registry pattern - no hardcoded addresses!');
