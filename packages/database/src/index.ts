import type { Prisma } from './generated/prisma';
import { PrismaClient } from './generated/prisma';

/**
 * Creates a new instance of the PrismaClientServer.
 *
 * @param connectionUrl The connection URL for the database.
 * @param options Optional Prisma client options.
 * @returns A PrismaClientServer instance.
 *
 * @example
 * ```typescript
 * import { createPrismaClient } from '@hopfun/database';
 *
 * const prisma = createPrismaClient(process.env.DATABASE_URL);
 * ```
 */
export const createPrismaClient = (
  connectionUrl: string,
  options?: Prisma.Subset<
    Prisma.PrismaClientOptions,
    Prisma.PrismaClientOptions
  >,
): PrismaClient => {
  return new PrismaClient({
    ...options,
    datasources: {
      ...options?.datasources,
      db: {
        ...options?.datasources?.db,
        url: connectionUrl,
      },
    },
  });
};

// Export the generated Prisma types and client
export * from './generated/prisma';
