# @hopfun/logger

A centralized logging package for the HopFun project using Pino.

## Features

- **Environment-aware**: Different configurations for development and production
- **Pretty printing**: Colored, human-readable logs in development
- **File rotation**: Daily log rotation in production
- **Performance**: Built on <PERSON>no, one of the fastest Node.js loggers
- **Flexible log levels**: Support for trace, debug, info, warn, error, fatal

## Installation

```bash
pnpm add @hopfun/logger
```

## Usage

```typescript
import { logger } from '@hopfun/logger';

// Basic logging
logger.info('Application started');
logger.debug('Debug information');
logger.warn('Warning message');
logger.error('Error occurred');

// With context
logger.info({ userId: '123', action: 'login' }, 'User logged in');

// Create context-specific logger
import { createContextLogger } from '@hopfun/logger';

const requestLogger = createContextLogger({
  requestId: 'abc-123',
  userId: 'user-456',
});
requestLogger.info('Processing request');
```

## Configuration

### Log Levels

The logger supports the following log levels (from most to least verbose):

- `trace`: Most detailed logging
- `debug`: Debugging information
- `info`: General information (default in production)
- `warn`: Warning messages
- `error`: Error messages
- `fatal`: Fatal errors
- `silent`: No logging

### Environment Variables

The logger respects the following environment variables:

#### `LOG_LEVEL`

Explicitly sets the logging level. Takes precedence over NODE_ENV-based defaults.

```bash
# Enable debug logging
LOG_LEVEL=debug pnpm dev

# Enable trace logging (most verbose)
LOG_LEVEL=trace pnpm dev

# Only show errors
LOG_LEVEL=error pnpm start
```

#### `NODE_ENV`

When LOG_LEVEL is not set, the logger uses NODE_ENV to determine the default level:

- `development`: Uses `debug` level with pretty printing
- `production` (or any other value): Uses `info` level with file logging

## Examples

### Development Mode

```bash
# Automatically uses debug level with pretty printing
NODE_ENV=development pnpm dev
```

Output:

```
[2025-08-06 23:00:00.000 +0700] DEBUG: Compiling template...
[2025-08-06 23:00:00.001 +0700] INFO: Template compiled successfully
```

### Production Mode with Debug

```bash
# Force debug logging in production
LOG_LEVEL=debug pnpm start
```

### Testing and Debugging

For debugging compilation or other processes:

```bash
# Enable debug logs for template compilation
LOG_LEVEL=debug pnpm compile-templates

# Or use the convenient debug script
pnpm compile-templates:debug
```

## Log Output

### Development / Debug Mode

- Colored console output with pretty formatting
- Human-readable timestamps
- No pid/hostname clutter

### Production Mode

- JSON structured logs
- File output with daily rotation:
  - `./logs/app.log`: All logs (info and above)
  - `./logs/error.log`: Error logs only
- Console output for debugging if needed

## Best Practices

1. **Use appropriate log levels**:
   - `debug`: Detailed information for debugging
   - `info`: Important events and state changes
   - `warn`: Recoverable issues that should be addressed
   - `error`: Errors that need immediate attention

2. **Include context**:

   ```typescript
   logger.info({ userId, orderId, amount }, 'Order placed');
   ```

3. **Use child loggers for request context**:

   ```typescript
   const requestLogger = logger.child({ requestId });
   requestLogger.info('Processing started');
   ```

4. **Avoid logging sensitive data**:

   ```typescript
   // Bad
   logger.info({ password }, 'User login');

   // Good
   logger.info({ userId }, 'User login');
   ```

## Troubleshooting

### Debug logs not showing?

1. **Check LOG_LEVEL environment variable**:

   ```bash
   echo $LOG_LEVEL
   # Should output: debug
   ```

2. **Set LOG_LEVEL explicitly**:

   ```bash
   LOG_LEVEL=debug pnpm dev
   ```

3. **Rebuild the logger package** (if you made changes):

   ```bash
   pnpm --filter @hopfun/logger build
   ```

4. **Check NODE_ENV**:
   ```bash
   echo $NODE_ENV
   # For debug logs without LOG_LEVEL, should be: development
   ```

### Logs not formatting correctly?

The logger uses `pino-pretty` for formatting. Ensure it's installed:

```bash
pnpm --filter @hopfun/logger add pino-pretty
```

## API Reference

### `logger`

Main logger instance with all Pino methods:

- `logger.trace(msg, ...args)`
- `logger.debug(msg, ...args)`
- `logger.info(msg, ...args)`
- `logger.warn(msg, ...args)`
- `logger.error(msg, ...args)`
- `logger.fatal(msg, ...args)`

### `createContextLogger(context)`

Creates a child logger with persistent context.

**Parameters:**

- `context`: Object with key-value pairs to include in all logs

**Returns:**

- Logger instance with context

## License

MIT
