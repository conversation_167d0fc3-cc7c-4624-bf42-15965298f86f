{"name": "@hopfun/logger", "version": "1.0.0", "description": "A logger package for the hopfun project.", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "tsup", "lint": "eslint .", "format": "prettier --write .", "format:check": "prettier --check ."}, "repository": {"type": "git", "url": "https://github.com/hopaggregator/hopfun.git", "directory": "packages/logger"}, "keywords": ["logger", "hopfun", "logging"], "author": "", "license": "MIT", "dependencies": {"dotenv": "catalog:", "pino": "catalog:", "pino-pretty": "catalog:", "pino-roll": "catalog:"}, "devDependencies": {"@hopfun/eslint": "workspace:*", "@hopfun/tsconfig": "workspace:*", "@types/node": "catalog:", "eslint": "catalog:", "prettier": "catalog:", "rimraf": "catalog:", "tsc-alias": "catalog:", "tsup": "catalog:", "typescript": "catalog:", "typescript-transform-paths": "catalog:"}}