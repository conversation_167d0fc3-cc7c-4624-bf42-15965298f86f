import 'dotenv/config';

import type { Logger } from 'pino';
import pino from 'pino';

const isDevelopment = process.env.NODE_ENV === 'development';

// Get log level from environment variable or default based on NODE_ENV
const getLogLevel = (): string => {
  // If LOG_LEVEL is explicitly set, use it
  if (process.env.LOG_LEVEL) {
    const validLevels = [
      'trace',
      'debug',
      'info',
      'warn',
      'error',
      'fatal',
      'silent',
    ];
    const level = process.env.LOG_LEVEL.toLowerCase();
    return validLevels.includes(level) ? level : 'info';
  }
  // Otherwise, use NODE_ENV-based defaults
  return isDevelopment ? 'debug' : 'info';
};

/**
 * Pino logger instance with configuration based on environment
 * - In development, it uses pretty printing for easier debugging
 * - In production, it writes logs to files with daily rotation
 * - LOG_LEVEL environment variable overrides default levels
 *
 * @example
 * import { logger } from '@hopfun/logger';
 * logger.info('This is an info message');
 * logger.debug('This is a debug message'); // Only shown when LOG_LEVEL=debug or NODE_ENV=development
 */
const logLevel = getLogLevel();
const isDebugMode = logLevel === 'debug' || logLevel === 'trace';

export const pinoInstance = pino({
  level: logLevel,
  timestamp: pino.stdTimeFunctions.isoTime,
  transport:
    isDevelopment || isDebugMode
      ? {
          target: 'pino-pretty',
          options: {
            colorize: true,
            translateTime: 'SYS:standard',
            ignore: 'pid,hostname',
          },
        }
      : {
          targets: [
            {
              target: 'pino-roll',
              options: {
                file: './logs/app.log',
                frequency: 'daily',
                mkdir: true,
              },
              level: 'info',
            },
            {
              target: 'pino-roll',
              options: {
                file: './logs/error.log',
                frequency: 'daily',
                mkdir: true,
              },
              level: 'error',
            },
            {
              target: 'pino-pretty',
              options: {
                colorize: false,
                mkdir: true,
                translateTime: 'SYS:standard',
              },
              level: 'debug',
            },
          ],
        },
});

export const logger = pinoInstance as Logger;

/**
 * Create a context-specific logger
 * @param context - The context to include in the log
 * @returns A logger instance with the context
 *
 * @example
 * const logger = createContextLogger({ userId: '12345', requestId: 'abcde' });
 * logger.info('This is an info message');
 * logger.error('This is an error message');
 */
export const createContextLogger = (context: Record<string, unknown>) => {
  return logger.child(context);
};
