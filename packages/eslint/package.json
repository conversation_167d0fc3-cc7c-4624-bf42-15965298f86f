{"name": "@hopfun/eslint", "version": "0.0.0", "license": "MIT", "private": true, "type": "module", "exports": {"./*": "./*.js"}, "devDependencies": {"@eslint/compat": "catalog:", "@eslint/eslintrc": "catalog:", "@eslint/js": "catalog:", "@next/eslint-plugin-next": "catalog:", "@types/eslint__eslintrc": "catalog:", "@types/eslint__js": "catalog:", "@typescript-eslint/eslint-plugin": "catalog:", "@typescript-eslint/parser": "catalog:", "eslint": "catalog:", "eslint-config-next": "catalog:", "eslint-config-prettier": "catalog:", "eslint-plugin-import": "catalog:", "eslint-plugin-jsx-a11y": "catalog:", "eslint-plugin-prettier": "catalog:", "eslint-plugin-react": "catalog:", "eslint-plugin-react-hooks": "catalog:", "eslint-plugin-simple-import-sort": "catalog:", "eslint-plugin-unicorn": "catalog:", "typescript-eslint": "catalog:"}}