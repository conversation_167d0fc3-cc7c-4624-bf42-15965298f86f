{
  // eslint extension options
  "eslint.enable": true,
  "eslint.validate": [
    "javascript",
    "javascriptreact",
    "typescript",
    "typescriptreact"
  ],
  "css.customData": [".vscode/tailwind.json"],
  // prettier extension setting
  "editor.formatOnSave": true,
  "[javascript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.formatOnSave": true
  },
  "[javascriptreact]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.formatOnSave": true
  },
  "[typescript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.formatOnSave": true
  },
  "[typescriptreact]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.formatOnSave": true
  },
  // prisma extension options
  "[prisma]": {
    "editor.defaultFormatter": "Prisma.prisma",
    "editor.formatOnSave": true
  },
  "editor.rulers": [80],
  "editor.codeActionsOnSave": [
    "source.addMissingImports",
    "source.fixAll",
    "source.organizeImports"
  ],
  // Show in vscode "Problems" tab when there are errors
  "typescript.tsserver.experimental.enableProjectDiagnostics": true,
  // Use absolute import for typescript files
  "typescript.preferences.importModuleSpecifier": "non-relative",
  // IntelliSense for taiwind variants
  "tailwindCSS.experimental.classRegex": [
    ["([\"'`][^\"'`]*.*?[\"'`])", "[\"'`]([^\"'`]*).*?[\"'`]"]
  ]
}
