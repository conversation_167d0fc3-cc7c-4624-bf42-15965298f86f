// Test the historical event backfill functionality
import { SuiClient } from '@mysten/sui/client';

const client = new SuiClient({
  url: 'https://fullnode.devnet.sui.io',
});

const hopfunPackageId =
  '0x497ff2769b28664d052601132542a2a77e75339945dc34a494e40fcd680065ac';

async function testBackfill() {
  console.log('🧪 Testing historical event backfill...');

  try {
    console.log('1️⃣ Querying events from HopFun events module...');

    // Try different query approaches
    console.log('🔍 Trying MoveModule filter...');
    const moduleEventsResponse = await client.queryEvents({
      query: {
        MoveModule: {
          package: hopfunPackageId,
          module: 'events',
        },
      },
      limit: 50,
      order: 'descending',
    });

    console.log(
      '📊 MoveModule filter results:',
      moduleEventsResponse.data.length,
    );

    console.log('🔍 Trying specific event type filter...');
    const specificEventsResponse = await client.queryEvents({
      query: {
        MoveEventType: `${hopfunPackageId}::events::BondingCurveCreated<0x2::sui::SUI>`,
      },
      limit: 50,
      order: 'descending',
    });

    console.log(
      '📊 Specific event type results:',
      specificEventsResponse.data.length,
    );

    console.log('🔍 Trying ConnectorCreated event type filter...');
    const connectorEventsResponse = await client.queryEvents({
      query: {
        MoveEventType: `${hopfunPackageId}::events::ConnectorCreated`,
      },
      limit: 50,
      order: 'descending',
    });

    console.log(
      '📊 ConnectorCreated event type results:',
      connectorEventsResponse.data.length,
    );

    // Use the response that has events
    let eventsResponse;
    if (connectorEventsResponse.data.length > 0) {
      console.log('✅ Using ConnectorCreated events');
      eventsResponse = connectorEventsResponse;
    } else if (specificEventsResponse.data.length > 0) {
      console.log('✅ Using specific BondingCurveCreated events');
      eventsResponse = specificEventsResponse;
    } else if (moduleEventsResponse.data.length > 0) {
      console.log('✅ Using MoveModule events');
      eventsResponse = moduleEventsResponse;
    } else {
      console.log('❌ No events found with any filter');
      return;
    }

    console.log('✅ Query successful');
    console.log('📊 Events found:', eventsResponse.data.length);

    if (eventsResponse.data.length === 0) {
      console.log('❌ No events found from HopFun events module');
      return;
    }

    // Process each event
    for (const [index, event] of eventsResponse.data.entries()) {
      console.log(
        `\n2️⃣ Processing event ${index + 1}/${eventsResponse.data.length}:`,
      );

      // Check if this is a HopFun event we care about
      const isHopfunEvent =
        event.packageId === hopfunPackageId &&
        event.type.includes(`${hopfunPackageId}::events::`);

      console.log('🔍 Event analysis:', {
        type: event.type,
        packageId: event.packageId,
        isHopfunEvent: isHopfunEvent,
        transactionDigest: event.id.txDigest,
        timestampMs: event.timestampMs,
      });

      if (!isHopfunEvent) {
        console.log('❌ Event filtered out (not a HopFun event)');
        continue;
      }

      // Get the event type for processing
      const eventTypes = {
        ConnectorCreated: 'ConnectorCreated',
        BondingCurveCreated: 'BondingCurveCreated',
        BondingCurveBuy: 'BondingCurveBuy',
        BondingCurveSell: 'BondingCurveSell',
        BondingCurveComplete: 'BondingCurveComplete',
        BondingCurveMigrate: 'BondingCurveMigrate',
        MemeConfigUpdated: 'MemeConfigUpdated',
      };

      // Handle generic type parameters
      const cleanEventType = event.type.split('<')[0];
      const eventName = cleanEventType.split('::').pop();
      const eventType =
        eventName && eventTypes.hasOwnProperty(eventName)
          ? eventTypes[eventName]
          : null;

      console.log('🎯 Event type mapping:', {
        originalType: event.type,
        cleanType: cleanEventType,
        eventName: eventName,
        mappedType: eventType,
      });

      if (!eventType) {
        console.log('❌ Event filtered out (unknown event type)');
        continue;
      }

      console.log(`✅ Event would be processed as: ${eventType}`);

      if (eventType === 'BondingCurveCreated') {
        console.log('🎉 Found BondingCurveCreated event!');
        console.log(
          '📄 Event data:',
          JSON.stringify(event.parsedJson, null, 2),
        );
      }
    }
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

testBackfill();
