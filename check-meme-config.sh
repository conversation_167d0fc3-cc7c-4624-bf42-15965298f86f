#!/bin/bash

echo "🔍 Checking MemeConfig object for connectors and dev orders..."

MEME_CONFIG_ID="0x864be1505bd5f86cae41741293d02860039007ebf2eeb1374c3dfebc246251d2"
RPC_URL="https://fullnode.devnet.sui.io"

echo "🎯 MemeConfig ID: $MEME_CONFIG_ID"

# Check the MemeConfig object
echo ""
echo "📊 Checking MemeConfig object details..."
curl -s -X POST "$RPC_URL" \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "id": 1,
    "method": "sui_getObject",
    "params": [
      "'$MEME_CONFIG_ID'",
      {
        "showContent": true,
        "showOwner": true,
        "showType": true
      }
    ]
  }' | jq '{
    exists: (.result.data != null),
    owner: .result.data.owner,
    type: .result.data.type,
    fields: .result.data.content.fields
  }'

echo ""
echo "🔍 Checking for objects owned by MemeConfig (including connectors)..."
curl -s -X POST "$RPC_URL" \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "id": 2,
    "method": "suix_getOwnedObjects",
    "params": [
      "'$MEME_CONFIG_ID'",
      {
        "filter": null,
        "options": {
          "showType": true,
          "showContent": true
        }
      }
    ]
  }' | jq '.result.data | length' | xargs -I {} echo "📦 Found {} objects owned by MemeConfig"

# Check for any connector objects specifically
echo ""
echo "🔗 Looking for Connector objects owned by MemeConfig..."
curl -s -X POST "$RPC_URL" \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "id": 3,
    "method": "suix_getOwnedObjects",
    "params": [
      "'$MEME_CONFIG_ID'",
      {
        "filter": {
          "StructType": "0x497ff2769b28664d052601132542a2a77e75339945dc34a494e40fcd680065ac::connector::Connector"
        },
        "options": {
          "showType": true,
          "showContent": true
        }
      }
    ]
  }' | jq '.result.data | length' | xargs -I {} echo "🔗 Found {} Connector objects"

# If there are connectors, show them
CONNECTOR_COUNT=$(curl -s -X POST "$RPC_URL" \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "id": 4,
    "method": "suix_getOwnedObjects",
    "params": [
      "'$MEME_CONFIG_ID'",
      {
        "filter": {
          "StructType": "0x497ff2769b28664d052601132542a2a77e75339945dc34a494e40fcd680065ac::connector::Connector"
        },
        "options": {
          "showType": true,
          "showContent": true
        }
      }
    ]
  }' | jq '.result.data | length')

if [ "$CONNECTOR_COUNT" -gt 0 ]; then
  echo ""
  echo "📋 Connector details:"
  curl -s -X POST "$RPC_URL" \
    -H "Content-Type: application/json" \
    -d '{
      "jsonrpc": "2.0",
      "id": 5,
      "method": "suix_getOwnedObjects",
      "params": [
        "'$MEME_CONFIG_ID'",
        {
          "filter": {
            "StructType": "0x497ff2769b28664d052601132542a2a77e75339945dc34a494e40fcd680065ac::connector::Connector"
          },
          "options": {
            "showType": true,
            "showContent": true
          }
        }
      ]
    }' | jq '.result.data[] | {
      objectId: .data.objectId,
      type: .data.type,
      temp_id: .data.content.fields.temp_id,
      creator: .data.content.fields.creator
    }'
fi

echo ""
echo "🏁 MemeConfig analysis complete!"
