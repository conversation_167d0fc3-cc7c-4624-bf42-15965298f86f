// Test the getEventType function with our specific BondingCurveCreated event

// Simulate the getEventType function logic
function getEventType(eventType) {
  const eventTypes = {
    ConnectorCreated: 'ConnectorCreated',
    BondingCurveCreated: 'BondingCurveCreated',
    BondingCurveBuy: 'BondingCurveBuy',
    BondingCurveSell: 'BondingCurveSell',
    BondingCurveComplete: 'BondingCurveComplete',
    BondingCurveMigrate: 'BondingCurveMigrate',
    MemeConfigUpdated: 'MemeConfigUpdated',
  };

  // Handle generic type parameters (e.g., BondingCurveCreated<0x...::template::TEMPLATE>)
  // Remove the generic type parameter if present
  const cleanEventType = eventType.split('<')[0];

  // Extract event name from full event type
  const eventName = cleanEventType.split('::').pop();

  console.log('Debug info:', {
    originalEventType: eventType,
    cleanEventType,
    eventName,
    hasProperty: eventName ? eventTypes.hasOwnProperty(eventName) : false,
    result: eventName && eventTypes.hasOwnProperty(eventName) ? eventTypes[eventName] : null,
  });

  if (eventName && eventTypes.hasOwnProperty(eventName)) {
    return eventTypes[eventName];
  }
  return null;
}

// Test with our specific event types
console.log('🧪 Testing event type mapping...\n');

// Test 1: ConnectorCreated (working)
const connectorEvent = '0x497ff2769b28664d052601132542a2a77e75339945dc34a494e40fcd680065ac::events::ConnectorCreated';
console.log('1️⃣ ConnectorCreated test:');
const connectorResult = getEventType(connectorEvent);
console.log('Result:', connectorResult);
console.log('✅ Expected: ConnectorCreated, Got:', connectorResult, connectorResult === 'ConnectorCreated' ? '✅' : '❌');
console.log('');

// Test 2: BondingCurveCreated with generic type (the problematic one)
const bondingCurveEvent = '0x497ff2769b28664d052601132542a2a77e75339945dc34a494e40fcd680065ac::events::BondingCurveCreated<0x89520186ab97c10ca31b861d40febc051ad6bb6d6d7091367dbc322fed5be256::template::TEMPLATE>';
console.log('2️⃣ BondingCurveCreated with generic test:');
const bondingCurveResult = getEventType(bondingCurveEvent);
console.log('Result:', bondingCurveResult);
console.log('✅ Expected: BondingCurveCreated, Got:', bondingCurveResult, bondingCurveResult === 'BondingCurveCreated' ? '✅' : '❌');
console.log('');

// Test 3: BondingCurveCreated without generic type
const bondingCurveSimple = '0x497ff2769b28664d052601132542a2a77e75339945dc34a494e40fcd680065ac::events::BondingCurveCreated';
console.log('3️⃣ BondingCurveCreated without generic test:');
const bondingCurveSimpleResult = getEventType(bondingCurveSimple);
console.log('Result:', bondingCurveSimpleResult);
console.log('✅ Expected: BondingCurveCreated, Got:', bondingCurveSimpleResult, bondingCurveSimpleResult === 'BondingCurveCreated' ? '✅' : '❌');
console.log('');

console.log('🏁 Event type mapping test complete!');
