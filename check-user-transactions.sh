#!/bin/bash

echo "🔍 Checking recent transactions from user wallet..."

USER_ADDRESS="0xfd78cd8b8351aff637c34a0272f26a3e694028890d1b637563b4aa2e90dd496a"
RPC_URL="https://fullnode.devnet.sui.io"

echo "👤 User Address: $USER_ADDRESS"

# Check recent transactions from this user
echo ""
echo "📊 Checking recent transactions from user..."
curl -s -X POST "$RPC_URL" \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "id": 1,
    "method": "suix_queryTransactionBlocks",
    "params": [
      {
        "filter": {
          "FromAddress": "'$USER_ADDRESS'"
        }
      },
      null,
      10,
      true
    ]
  }' | jq '.result.data | length' | xargs -I {} echo "📋 Found {} recent transactions from user"

# Get details of recent transactions
echo ""
echo "🔍 Analyzing recent transactions..."
RECENT_TXS=$(curl -s -X POST "$RPC_URL" \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "id": 2,
    "method": "suix_queryTransactionBlocks",
    "params": [
      {
        "filter": {
          "FromAddress": "'$USER_ADDRESS'"
        }
      },
      null,
      5,
      true
    ]
  }' | jq -r '.result.data[]')

if [ "$RECENT_TXS" != "" ]; then
  echo "$RECENT_TXS" | jq '{
    digest: .digest,
    timestamp: .timestampMs,
    status: .effects.status.status,
    gasUsed: .effects.gasUsed,
    error: (if .effects.status.status == "failure" then .effects.status.error else null end)
  }'
else
  echo "❌ No recent transactions found from user"
fi

echo ""
echo "🔍 Looking for failed transactions specifically..."
curl -s -X POST "$RPC_URL" \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "id": 3,
    "method": "suix_queryTransactionBlocks",
    "params": [
      {
        "filter": {
          "FromAddress": "'$USER_ADDRESS'"
        }
      },
      null,
      20,
      true
    ]
  }' | jq '.result.data[] | select(.effects.status.status == "failure") | {
    digest: .digest,
    timestamp: .timestampMs,
    error: .effects.status.error,
    gasUsed: .effects.gasUsed
  }'

echo ""
echo "🏁 User transaction analysis complete!"
