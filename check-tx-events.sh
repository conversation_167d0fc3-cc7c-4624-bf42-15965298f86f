#!/bin/bash

echo "🔍 Checking events from successful accept_connector transaction..."

TX_DIGEST="8h92uSdBYsarWqdZej2aDMpxFrxLh3hg6UgsxFHG16rY"
RPC_URL="https://fullnode.devnet.sui.io"

echo "📋 Transaction: $TX_DIGEST"

curl -s -X POST "$RPC_URL" \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "id": 1,
    "method": "sui_getTransactionBlock",
    "params": [
      "'$TX_DIGEST'",
      {
        "showEvents": true,
        "showObjectChanges": true
      }
    ]
  }' | jq '{
    events: .result.events,
    objectChanges: .result.objectChanges
  }'
