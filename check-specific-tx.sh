#!/bin/bash

echo "🔍 Checking specific transaction details..."

# Check the most recent transaction
TX_DIGEST="8h92uSdBYsarWqdZej2aDMpxFrxLh3hg6UgsxFHG16rY"
RPC_URL="https://fullnode.devnet.sui.io"

echo "📋 Transaction: $TX_DIGEST"

curl -s -X POST "$RPC_URL" \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "id": 1,
    "method": "sui_getTransactionBlock",
    "params": [
      "'$TX_DIGEST'",
      {
        "showEffects": true,
        "showEvents": true,
        "showInput": true,
        "showObjectChanges": true
      }
    ]
  }' | jq '{
    digest: .result.digest,
    status: .result.effects.status,
    gasUsed: .result.effects.gasUsed,
    events: (.result.events | length),
    objectChanges: (.result.objectChanges | length),
    input: .result.transaction.data.transaction
  }'
