# MongoDB Docker Compose Setup Guide

## Overview

This is a production-ready MongoDB setup using Docker Compose with best practices for scalability, security, performance, and maintainability. The setup includes:

- **MongoDB 7.0** with replica set configuration
- **Automatic initialization** with collections, indexes, and users
- **MongoDB Express** for web-based management (development only)
- **Prometheus monitoring** with MongoDB Exporter
- **Automated backups** with retention policies
- **Separate configurations** for development and production environments

## Quick Start

### 1. Prerequisites

- Docker and Docker Compose installed
- MongoDB Compass (optional, for GUI access)
- At least 2GB of available RAM
- 10GB of available disk space

### 2. Initial Setup

```bash
# Clone the repository (if not already done)
git clone <repository-url>
cd hopfun

# Copy environment variables
cp .env.example .env

# Edit .env file with your secure passwords
# IMPORTANT: Change all default passwords before production use!
nano .env

# Ensure MongoDB keyfile has correct permissions
chmod 400 scripts/mongodb-keyfile
```

### 3. Start MongoDB

#### Development Environment

```bash
# Start MongoDB with development settings (uses docker-compose.override.yml automatically)
docker-compose up -d

# View logs
docker-compose logs -f mongodb-primary

# Check status
docker-compose ps
```

#### Production Environment

```bash
# Start MongoDB with production settings
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d

# This will start:
# - 1 Primary node
# - 2 Secondary nodes  
# - 1 Arbiter node
# - MongoDB Exporter for monitoring
# - Automated backup service
```

### 4. Connect to MongoDB

#### Using MongoDB Compass

1. Open MongoDB Compass
2. Use this connection string:
   ```
   *************************************************************************
   ```
3. Replace `admin` and `password` with your credentials from `.env`

#### Using Application

```javascript
// Node.js example
const connectionString = '*******************************************************************************';
```

#### Using MongoDB Shell

```bash
# Connect to MongoDB shell
docker-compose exec mongodb-primary mongosh -u admin -p password --authenticationDatabase admin

# Show databases
show dbs

# Use hopfun database
use hopfun

# Show collections
show collections
```

## Architecture

### Service Components

1. **mongodb-primary**: Main MongoDB instance (always running)
2. **mongodb-setup**: One-time replica set initialization
3. **mongo-express**: Web UI for MongoDB (dev only, port 8081)
4. **mongodb-exporter**: Prometheus metrics exporter (port 9216)
5. **mongodb-backup**: Manual/scheduled backup service

### Database Structure

```
hopfun (main database)
├── tokens              # Token information with validation
├── tokenStats          # 24h volume and transaction stats
├── userHoldings        # User portfolio positions
├── tokenTransactions   # Transaction history
├── bondingCurve*       # Bonding curve events
├── connectorCreated    # Connector events
├── indexerState        # Blockchain indexing state
├── processingQueue     # Event processing queue
└── systemLogs          # Capped collection for logs
```

### Indexes Created

- **Performance indexes** on frequently queried fields
- **Unique constraints** on addresses and IDs
- **Compound indexes** for complex queries
- **TTL indexes** for automatic data cleanup
- **Text indexes** for search functionality

## Configuration

### Environment Variables

Key variables in `.env`:

```bash
# MongoDB Credentials
MONGO_ROOT_USERNAME=admin
MONGO_ROOT_PASSWORD=changeme_in_production

# Performance Tuning
MONGO_CACHE_SIZE_GB=1            # WiredTiger cache size
MONGO_CPU_LIMIT=2                # CPU limit for container
MONGO_MEMORY_LIMIT=2G            # Memory limit

# Backup Settings
BACKUP_RETENTION_DAYS=7          # Keep backups for N days
BACKUP_SCHEDULE="0 2 * * *"      # Cron schedule

# Monitoring
MONGO_EXPORTER_PORT=9216         # Prometheus metrics port
```

### Security Features

1. **Authentication**: Required for all connections
2. **Keyfile Authentication**: For replica set members
3. **Role-Based Access**: Multiple users with different permissions
4. **Network Isolation**: Services communicate via Docker network
5. **Resource Limits**: CPU and memory constraints

### Created Users

The initialization creates these users:

- **admin**: Root administrator (full access)
- **hopfun_app**: Application user (read/write to hopfun database)
- **hopfun_readonly**: Analytics user (read-only access)
- **hopfun_backup**: Backup user (backup/restore permissions)

## Operations

### Backup and Restore

#### Manual Backup

```bash
# Run manual backup
docker-compose run --rm mongodb-backup

# Backups are stored in ./backups/mongodb/
ls -la ./backups/mongodb/
```

#### Scheduled Backups (Production)

Automatically runs daily at 2 AM (configurable via `BACKUP_SCHEDULE`)

#### Restore from Backup

```bash
# Extract backup
tar -xzf ./backups/mongodb/hopfun_20240101_120000.tar.gz -C ./backups/mongodb/

# Restore using mongorestore
docker-compose exec mongodb-primary mongorestore \
  --username admin \
  --password password \
  --authenticationDatabase admin \
  --db hopfun \
  --drop \
  /backups/hopfun_20240101_120000/hopfun
```

### Monitoring

#### MongoDB Express (Development)

Access at: http://localhost:8081
- Username: admin
- Password: admin

#### Prometheus Metrics

Access metrics at: http://localhost:9216/metrics

Key metrics to monitor:
- `mongodb_up`: MongoDB availability
- `mongodb_connections`: Active connections
- `mongodb_memory`: Memory usage
- `mongodb_opcounters`: Operation counts
- `mongodb_repl_lag`: Replication lag

#### Health Checks

```bash
# Check replica set status
docker-compose exec mongodb-primary mongosh \
  -u admin -p password \
  --authenticationDatabase admin \
  --eval "rs.status()"

# Check database stats
docker-compose exec mongodb-primary mongosh \
  -u admin -p password \
  --authenticationDatabase admin \
  --eval "db.stats()"
```

### Scaling

#### Add Secondary Node

1. Update `docker-compose.prod.yml` to add new secondary
2. Run: `docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d`
3. Add to replica set:
```javascript
rs.add("mongodb-secondary3:27017")
```

#### Performance Tuning

Adjust in `.env`:
- `MONGO_CACHE_SIZE_GB`: Increase for better performance
- `MONGO_CPU_LIMIT`: Increase for heavy workloads
- `MONGO_MEMORY_LIMIT`: Should be cache size + 1GB overhead

## Troubleshooting

### Common Issues

#### 1. MongoDB Won't Start

```bash
# Check logs
docker-compose logs mongodb-primary

# Check permissions on keyfile
ls -la scripts/mongodb-keyfile
# Should be: -r-------- (400)

# Fix permissions
chmod 400 scripts/mongodb-keyfile
```

#### 2. Cannot Connect from MongoDB Compass

- Ensure MongoDB is running: `docker-compose ps`
- Check firewall allows port 27017
- Verify credentials in connection string
- Include `replicaSet=rs0` in connection string

#### 3. Replica Set Not Initialized

```bash
# Manually run initialization
docker-compose run --rm mongodb-setup

# Check replica set status
docker-compose exec mongodb-primary mongosh \
  -u admin -p password \
  --authenticationDatabase admin \
  --eval "rs.status()"
```

#### 4. High Memory Usage

```bash
# Check current memory usage
docker stats mongodb-primary

# Adjust cache size in .env
MONGO_CACHE_SIZE_GB=0.5  # Reduce for development

# Restart MongoDB
docker-compose restart mongodb-primary
```

### Logs and Debugging

```bash
# View all logs
docker-compose logs

# Follow specific service logs
docker-compose logs -f mongodb-primary

# Check container resource usage
docker stats

# Enter MongoDB container
docker-compose exec mongodb-primary bash

# Run MongoDB shell commands
docker-compose exec mongodb-primary mongosh \
  -u admin -p password \
  --authenticationDatabase admin
```

## Best Practices

### Security

1. **Change default passwords** immediately
2. **Use strong passwords** (minimum 16 characters)
3. **Rotate credentials** regularly
4. **Limit network exposure** (bind to localhost in production)
5. **Enable TLS/SSL** for production deployments
6. **Regular security updates** of MongoDB image

### Performance

1. **Allocate sufficient memory** (50% of system RAM for cache)
2. **Use SSD storage** for data volumes
3. **Monitor slow queries** via operation profiling
4. **Create appropriate indexes** for your queries
5. **Regular maintenance** (compact, reindex)

### Backup Strategy

1. **Daily automated backups** minimum
2. **Test restore procedures** regularly
3. **Off-site backup storage** (S3, cloud storage)
4. **Multiple retention periods** (daily, weekly, monthly)
5. **Document recovery procedures**

### Monitoring

1. **Set up alerts** for key metrics
2. **Monitor replication lag** in production
3. **Track connection pool usage**
4. **Watch for slow queries**
5. **Monitor disk space** usage

## Maintenance

### Regular Tasks

#### Weekly
- Review slow query logs
- Check backup integrity
- Monitor disk space

#### Monthly
- Review and optimize indexes
- Analyze collection statistics
- Update MongoDB image if needed

#### Quarterly
- Security audit
- Performance review
- Capacity planning

### Upgrade MongoDB Version

```bash
# 1. Backup everything first!
docker-compose run --rm mongodb-backup

# 2. Stop services
docker-compose down

# 3. Update image version in docker-compose.yml
# Change: image: mongo:7.0 to image: mongo:7.1

# 4. Start services
docker-compose up -d

# 5. Verify upgrade
docker-compose exec mongodb-primary mongosh \
  -u admin -p password \
  --authenticationDatabase admin \
  --eval "db.version()"
```

## Support

For issues or questions:
1. Check the troubleshooting section above
2. Review MongoDB logs: `docker-compose logs mongodb-primary`
3. Consult [MongoDB documentation](https://docs.mongodb.com/)
4. Open an issue in the repository

## License

This configuration is provided as-is for the HopFun project. Ensure compliance with MongoDB licensing terms for production use.