# Docker Compose Override for Development Environment
# This file is automatically loaded by docker-compose

services:
  # MongoDB with development-friendly settings
  mongodb:
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password
      MONGO_INITDB_DATABASE: hopfun

  # MongoDB Express UI - enabled by default in dev
  mongo-express:
    profiles: []  # Remove profile restriction for dev
    environment:
      ME_CONFIG_MONGODB_ADMINUSERNAME: admin
      ME_CONFIG_MONGODB_ADMINPASSWORD: password
      ME_CONFIG_BASICAUTH_USERNAME: admin
      ME_CONFIG_BASICAUTH_PASSWORD: admin