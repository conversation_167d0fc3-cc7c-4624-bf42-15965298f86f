{"name": "@hopfun/monorepo", "version": "1.0.0", "private": true, "type": "module", "scripts": {"build": "nx run-many -t build", "dev:frontend": "nx run-many -t dev -p frontend", "dev:indexer": "nx run-many -t dev -p indexer", "dev:server": "nx run-many -t dev -p server", "db:migrate": "nx run @hopfun/database:db:migrate", "db:studio": "nx run @hopfun/database:db:studio", "lint": "nx run-many -t lint", "format": "nx run-many -t format", "format:check": "nx run-many -t format:check", "test": "nx run-many -t test", "type-check": "nx run-many -t type-check", "clean:node_modules": "rimraf apps/*/node_modules && rimraf packages/*/node_modules && rimraf node_modules"}, "devDependencies": {"prettier": "catalog:", "rimraf": "catalog:", "typescript": "catalog:", "nx": "catalog:"}, "packageManager": "pnpm@10.13.1"}