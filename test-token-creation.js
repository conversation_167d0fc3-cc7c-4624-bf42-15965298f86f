// Test script to debug token creation issues
// This script will help us understand what's failing in the token creation process

import { SuiClient } from '@mysten/sui/client';
import { Transaction } from '@mysten/sui/transactions';

const client = new SuiClient({
  url: 'https://fullnode.devnet.sui.io',
});

async function testTokenCreation() {
  console.log('🧪 Testing token creation process...');
  
  try {
    // Test 1: Check if we can connect to the network
    console.log('1️⃣ Testing network connection...');
    const chainId = await client.getChainIdentifier();
    console.log('✅ Connected to chain:', chainId);
    
    // Test 2: Check if the HopFun package exists
    console.log('2️⃣ Testing HopFun package access...');
    const hopfunPackageId = '0x497ff2769b28664d052601132542a2a77e75339945dc34a494e40fcd680065ac';
    
    try {
      const packageInfo = await client.getObject({
        id: hopfunPackageId,
        options: { showContent: true }
      });
      console.log('✅ HopFun package found:', packageInfo.data?.objectId);
    } catch (error) {
      console.error('❌ HopFun package not found:', error.message);
    }
    
    // Test 3: Check recent ConnectorCreated events
    console.log('3️⃣ Checking recent ConnectorCreated events...');
    const connectorEvents = await client.queryEvents({
      query: {
        MoveEventType: `${hopfunPackageId}::events::ConnectorCreated`
      },
      limit: 5,
      order: 'descending'
    });
    
    console.log(`✅ Found ${connectorEvents.data.length} recent ConnectorCreated events`);
    if (connectorEvents.data.length > 0) {
      console.log('📄 Latest ConnectorCreated event:', {
        digest: connectorEvents.data[0].id.txDigest,
        timestamp: connectorEvents.data[0].timestampMs,
        data: connectorEvents.data[0].parsedJson
      });
    }
    
    // Test 4: Check for BondingCurveCreated events
    console.log('4️⃣ Checking recent BondingCurveCreated events...');
    try {
      const bondingCurveEvents = await client.queryEvents({
        query: {
          MoveEventType: `${hopfunPackageId}::events::BondingCurveCreated`
        },
        limit: 5,
        order: 'descending'
      });
      
      console.log(`✅ Found ${bondingCurveEvents.data.length} recent BondingCurveCreated events`);
      if (bondingCurveEvents.data.length > 0) {
        console.log('📄 Latest BondingCurveCreated event:', {
          digest: bondingCurveEvents.data[0].id.txDigest,
          timestamp: bondingCurveEvents.data[0].timestampMs,
          data: bondingCurveEvents.data[0].parsedJson
        });
      } else {
        console.log('⚠️ No BondingCurveCreated events found - this confirms the issue!');
      }
    } catch (error) {
      console.error('❌ Error querying BondingCurveCreated events:', error.message);
    }
    
    // Test 5: Check if we can find any recent transactions that might have failed
    if (connectorEvents.data.length > 0) {
      console.log('5️⃣ Analyzing recent connector creation transaction...');
      const latestConnectorTx = connectorEvents.data[0].id.txDigest;
      
      try {
        const txDetails = await client.getTransactionBlock({
          digest: latestConnectorTx,
          options: {
            showEffects: true,
            showEvents: true,
            showObjectChanges: true,
            showInput: true
          }
        });
        
        console.log('📊 Transaction details:', {
          digest: txDetails.digest,
          status: txDetails.effects?.status?.status,
          gasUsed: txDetails.effects?.gasUsed,
          eventCount: txDetails.events?.length || 0,
          objectChanges: txDetails.objectChanges?.length || 0
        });
        
        // Look for any error messages
        if (txDetails.effects?.status?.status !== 'success') {
          console.error('❌ Transaction failed:', txDetails.effects?.status);
        }
        
        // Check what objects were created
        const createdObjects = txDetails.objectChanges?.filter(change => change.type === 'created') || [];
        console.log('🏗️ Objects created:', createdObjects.map(obj => ({
          type: obj.objectType,
          id: obj.objectId
        })));
        
      } catch (error) {
        console.error('❌ Error getting transaction details:', error.message);
      }
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

testTokenCreation();
